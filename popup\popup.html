<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>Form Analyzer - Report Selection</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      padding: 15px;
      margin: 0;
      background-color: #fff;
      color: #333;
      width: 300px; /* Adjust width as needed */
      font-size: 14px;
    }
    h1 {
      font-size: 16px; /* Slightly smaller */
      margin-top: 0;
      margin-bottom: 10px;
      color: #333;
      text-align: center;
    }
    select {
      width: 100%;
      padding: 8px;
      margin-bottom: 15px;
      border: 1px solid #ccc;
      border-radius: 4px;
      box-sizing: border-box;
      font-size: 13px;
    }
    button {
      background-color: #007bff; /* Primary blue */
      color: white;
      padding: 8px 15px; /* Adjust padding */
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;
      width: 100%; /* Make button full width */
      box-sizing: border-box;
      transition: background-color 0.2s ease;
    }
    button:hover {
      background-color: #0056b3; /* Darker blue */
    }
    button:disabled {
      background-color: #cccccc;
      cursor: not-allowed;
    }
    .message {
      margin-top: 15px;
      padding: 10px;
      border-radius: 4px;
      font-size: 12px;
      border-left: 4px solid;
      display: none; /* Hide by default */
      text-align: center;
    }
    .message.error {
      border-left-color: #f44336; /* Red */
      background-color: #fdd; /* Light red */
      color: #7f0000;
    }
    .message:not(.error) {
      border-left-color: #4CAF50; /* Green */
      background-color: #e8f5e9; /* Light green */
      color: #1b5e20;
    }
    body.loading {
      opacity: 0.7;
      pointer-events: none;
    }
  </style>
</head>
<body>
  <h1 style="text-align: left;">Select Report</h1>

  <select id="reportSelect">
    <option value="">-- Select an incomplete report --</option>
    <!-- Reports will be populated here -->
  </select>

  <h1 style="text-align: left; margin-top: 15px;">AI Settings</h1>

  <select id="aiModelSelect">
    <option value="openai">OpenAI (GPT-4o-mini)</option>
    <option value="gemini">Google Gemini</option>
  </select>

  <button id="saveButton" disabled>Save & Open Analyzer</button>

  <div id="message" class="message">
    <!-- Status messages will appear here -->
  </div>

  <!-- Scripts -->
  <script src="../lib/api-service.js"></script>
  <script src="popup.js"></script>
</body>
</html>