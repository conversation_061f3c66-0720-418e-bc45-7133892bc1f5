// content-actions-utils.js - Utility functions for content actions
// Handles: getFullHTML and other utility functions

// <PERSON><PERSON> getting the full HTML of an element
function handleGetFullHTML(message, sendResponse) {
    const selector = message.payload?.selector;
    console.log(`Content-Actions: Attempting to get full HTML for element with selector: "${selector}"`);

    if (selector) {
        try {
            const element = document.querySelector(selector);
            if (element) {
                console.log(`Content-Actions: Element found for selector "${selector}"`);
                sendResponse({ success: true, html: element.outerHTML });
            } else {
                console.warn(`Content-Actions: Element not found for selector: "${selector}"`);
                sendResponse({ success: false, error: 'Element not found.' });
            }
        } catch (e) {
            console.error(`Content-Actions: Error in getFullHTML for selector "${selector}":`, e);
            sendResponse({ success: false, error: `Execution error: ${e.message}` });
        }
    } else {
        sendResponse({ success: false, error: 'Missing selector for getFullHTML.' });
    }
}

// Make the functions available globally
window.handleGetFullHTML = handleGetFullHTML;

console.log('Content-Actions-Utils: Utility functions loaded');
