// File: admin/sidepanel/sidepanel-ui-admin-events.js

function setupGlobalSelectorEventListeners() {
    console.log("Setting up global event delegation for accordion headers");

    // Add global mouseenter event delegation for accordion headers (field and option)
    document.addEventListener('mouseenter', (e) => {
        // Check if this is a field header (h3) or option header (h4)
        if (e.target && e.target.tagName &&
            ((e.target.tagName.toLowerCase() === 'h3' && e.target.classList.contains('field-header')) ||
             (e.target.tagName.toLowerCase() === 'h4' && e.target.classList.contains('option-header')))) {

            let selectorInput = null;
            let container = null;

            // Find the associated container based on the header type
            if (e.target.tagName.toLowerCase() === 'h3') {
                // For field headers, find the container
                container = e.target.closest('.dynamic-field-container');
                if (container) {
                    // First try to find the field selector
                    selectorInput = container.querySelector('.field-selector');

                    // If field selector is empty or doesn't exist, look for option selectors
                    if (!selectorInput || !selectorInput.value.trim()) {
                        // Find the options container
                        const optionsContainer = container.querySelector('.options-container');
                        if (optionsContainer) {
                            // Find all option selectors with non-empty values
                            const optionSelectors = Array.from(optionsContainer.querySelectorAll('.option-selector'))
                                .filter(selector => selector.value.trim());

                            // For checkbox-group, radio-group, custom-checkbox-group, or custom-radio-group,
                            // highlight all option selectors
                            const fieldTypeSelect = container.querySelector('.field-type');
                            const fieldType = fieldTypeSelect ? fieldTypeSelect.value : '';

                            if ((fieldType === 'checkbox-group' || fieldType === 'radio-group' ||
                                 fieldType === 'custom-checkbox-group' || fieldType === 'custom-radio-group') &&
                                optionSelectors.length > 0) {
                                // Get all option selector values
                                const selectorValues = optionSelectors
                                    .map(selector => selector.value.trim())
                                    .filter(value => value); // Filter out empty values

                                // Use the new function to highlight all elements at once
                                chrome.runtime.sendMessage({
                                    action: 'proxyRequestToContentScript',
                                    targetTabId: state.currentTabId,
                                    contentScriptAction: 'highlightMultipleElementsBySelectors',
                                    payload: { selectors: selectorValues }
                                }).catch(error => {
                                    console.error('Error highlighting multiple elements on header hover:', error);
                                });

                                return; // Exit early since we've already sent the highlight request
                            } else if (optionSelectors.length > 0) {
                                // For other field types, use the first non-empty option selector
                                selectorInput = optionSelectors[0];
                            }
                        }
                    }
                }
            } else {
                // For option headers, find the option-selector in the same container
                container = e.target.closest('.option-item');
                if (container) {
                    selectorInput = container.querySelector('.option-selector');

                    // For option headers, we always use the individual option selector
                    // No special handling needed for checkbox/radio groups at the option level
                }
            }

            // Only highlight if we found a selector input with a value
            if (selectorInput && selectorInput.value.trim()) {
                chrome.runtime.sendMessage({
                    action: 'proxyRequestToContentScript',
                    targetTabId: state.currentTabId,
                    contentScriptAction: 'highlightElementBySelector',
                    payload: { selector: selectorInput.value.trim(), isValid: true }
                }).catch(error => console.error('Error highlighting element on header hover:', error));
            }
        }
    }, true); // Use capture phase

    // Add global mouseleave event delegation for accordion headers
    document.addEventListener('mouseleave', (e) => {
        if (e.target && e.target.tagName &&
            ((e.target.tagName.toLowerCase() === 'h3' && e.target.classList.contains('field-header')) ||
             (e.target.tagName.toLowerCase() === 'h4' && e.target.classList.contains('option-header')))) {

            // Remove all highlights when mouse leaves the header
            chrome.runtime.sendMessage({
                action: 'proxyRequestToContentScript',
                targetTabId: state.currentTabId,
                contentScriptAction: 'removeAllHighlights',
                payload: {}
            }).catch(error => {
                console.error('Error removing all highlights on header leave:', error);
            });
        }
    }, true); // Use capture phase


    // Use event delegation to catch changes in option selectors and labels to update the dropdown
    // This duplicates some logic from createAdminOptionElement but is needed for delegated events
    document.addEventListener('input', (e) => {
        // Check if this is an option selector input
        if (e.target && e.target.classList && e.target.classList.contains('option-selector')) {
            console.log("[GLOBAL HANDLER] Option selector input:", e.target.value);
             const selectorInput = e.target;

            // Call the debounced highlight function
            debouncedHighlightSelector(selectorInput, false); // Assumes debouncedHighlightSelector is available

            // Find the parent field container to get the field ID
            const optionItem = selectorInput.closest('.option-item');
            const fieldContainer = optionItem?.closest('.dynamic-field-container');

            if (fieldContainer) {
                const fieldId = fieldContainer.getAttribute('data-field-id') || `dynamic-field-${fieldContainer.getAttribute('data-field-index')}`;
                // Use the centralized update function
                console.log(`[GLOBAL HANDLER] Updating default option dropdown for field ${fieldId}`);
                updateDefaultOptionDropdown(fieldId); // Assumes updateDefaultOptionDropdown is available

                // Force another update after a short delay to ensure it's processed
                setTimeout(() => {
                    console.log(`[GLOBAL HANDLER] Forcing second update of default option dropdown`);
                    updateDefaultOptionDropdown(fieldId);
                }, 200);
            }

            // Update the accordion header state after validation
            if (typeof updateAccordionHeaderState === 'function') {
                // Use setTimeout to ensure validation has completed
                setTimeout(() => updateAccordionHeaderState(selectorInput), 350);
            }
        }
        // Check if this is an option label input
        else if (e.target && e.target.classList && e.target.classList.contains('option-label')) {
            console.log("[GLOBAL HANDLER] Option label input:", e.target.value);
            const labelInput = e.target;

            // Find the parent option item and field container
            const optionItem = labelInput.closest('.option-item');
            const fieldContainer = optionItem?.closest('.dynamic-field-container');

            if (optionItem && fieldContainer) {
                // Update the option header text
                const optionIndex = optionItem.getAttribute('data-option-index');
                const headerText = optionItem.querySelector('h4 > span');
                if (headerText) {
                    const newLabel = labelInput.value || `Option ${parseInt(optionIndex) + 1}`;
                    console.log(`[GLOBAL HANDLER] Updating option header from "${headerText.textContent}" to "${newLabel}"`);
                    headerText.textContent = newLabel;
                }

                // Update the default option dropdown
                const fieldId = fieldContainer.getAttribute('data-field-id') || `dynamic-field-${fieldContainer.getAttribute('data-field-index')}`;
                console.log(`[GLOBAL HANDLER] Updating default option dropdown for field ${fieldId} (from option label)`);
                updateDefaultOptionDropdown(fieldId);

                // Force another update after a short delay to ensure it's processed
                setTimeout(() => {
                    console.log(`[GLOBAL HANDLER] Forcing second update of default option dropdown after label change`);
                    updateDefaultOptionDropdown(fieldId);
                }, 200);
            }
        }
        else if (e.target && e.target.classList && e.target.classList.contains('field-selector')) {
             // Also handle input on field selectors
             const selectorInput = e.target;
             debouncedHighlightSelector(selectorInput, false); // Assumes debouncedHighlightSelector is available
             // Find the parent field container to get the field ID
             const fieldContainer = selectorInput.closest('.dynamic-field-container');
             if (fieldContainer) {
                 const fieldId = fieldContainer.getAttribute('data-field-id') || `dynamic-field-${fieldContainer.getAttribute('data-field-index')}`;
                  // Trigger update for the default option dropdown just in case (though it's option selectors that matter most)
                  console.log(`[GLOBAL HANDLER] Updating default option dropdown for field ${fieldId} (from field selector)`);
                  updateDefaultOptionDropdown(fieldId);
             }

             // Update the accordion header state after validation
             if (typeof updateAccordionHeaderState === 'function') {
                 // Use setTimeout to ensure validation has completed
                 setTimeout(() => updateAccordionHeaderState(selectorInput), 350);
             }
        }
    }, true); // Use capture phase


    // Handle paste events - trigger input event after paste completes
    document.addEventListener('paste', (e) => {
        if (e.target && e.target.classList && (e.target.classList.contains('field-selector') || e.target.classList.contains('option-selector'))) {
            // Wait for the paste to complete
            setTimeout(() => {
                console.log("[GLOBAL HANDLER] Paste detected in selector:", e.target.value);
                // Trigger the input event to use the same handler
                e.target.dispatchEvent(new Event('input', { bubbles: true }));
            }, 0);
        }
    }, true); // Use capture phase

    console.log("Global selector event delegation set up");
}


// Function to ensure all selector inputs have proper initial validation
// Hover listeners are now handled by global delegation, so this focuses on validation
// Returns a Promise that resolves when all validations are complete
function ensureAllSelectorInputsAreValidated() {
    console.log("Ensuring all selector inputs are validated");
    // Find all selector inputs
    const allSelectorInputs = document.querySelectorAll('.field-selector, .option-selector');

    // Remove all validation classes from inputs
    allSelectorInputs.forEach(input => {
        // Remove all validation classes
        input.classList.remove('valid-selector', 'invalid-selector', 'missing-selector');

        // Remove all validation classes from containers
        const container = input.closest('.dynamic-field-container, .option-item');
        if (container && typeof updateAccordionHeaderState === 'function') {
            container.classList.remove('has-valid-selector', 'has-invalid-selector', 'has-missing-selector');
            // console.log('Removed validation classes from container:', container);
        }
    });

    // Return a promise that resolves when all validations are complete
    return new Promise(resolve => {
        // Track how many validations are pending
        let pendingValidations = allSelectorInputs.length;

        // If no inputs to validate, resolve immediately
        if (pendingValidations === 0) {
            console.log("No selector inputs to validate");
            resolve();
            return;
        }

        // Function to decrement counter and resolve when all are done
        const validationComplete = () => {
            pendingValidations--;
            if (pendingValidations <= 0) {
                console.log("All selector validations complete");
                resolve();
            }
        };

        allSelectorInputs.forEach(input => {
            // For empty inputs, we already applied the class, just count it as done
            if (!input.value.trim()) {
                validationComplete();
                return;
            }

            // For non-empty inputs, trigger validation
            setTimeout(() => {
                // Use the non-debounced version for immediate highlighting
                highlightSelectorElement(input, false).then(() => {
                    // Also ensure the related default option dropdown is updated if this is an option selector
                    if (input.classList.contains('option-selector')) {
                        const optionItem = input.closest('.option-item');
                        const fieldContainer = optionItem?.closest('.dynamic-field-container');
                        if (fieldContainer) {
                            const fieldId = fieldContainer.getAttribute('data-field-id') || `dynamic-field-${fieldContainer.getAttribute('data-field-index')}`;
                            console.log(`Updating default option dropdown for field ${fieldId} during validation`);
                            updateDefaultOptionDropdown(fieldId); // Assumes updateDefaultOptionDropdown is available

                            // Force another update after a short delay to ensure it's processed
                            setTimeout(() => {
                                console.log(`Forcing second update of default option dropdown during validation`);
                                updateDefaultOptionDropdown(fieldId);
                            }, 200);
                        }
                    } else if (input.classList.contains('field-selector')) {
                        const fieldContainer = input.closest('.dynamic-field-container');
                        if (fieldContainer) {
                            const fieldId = fieldContainer.getAttribute('data-field-id') || `dynamic-field-${fieldContainer.getAttribute('data-field-index')}`;
                            console.log(`Updating default option dropdown for field ${fieldId} during validation (from field selector)`);
                            updateDefaultOptionDropdown(fieldId); // Update field default option dropdown
                        }
                    }

                    // Mark this validation as complete
                    validationComplete();
                });
            }, 50); // Small delay
        });
    });
}


// Set up the global event listeners and observer when the document is loaded
document.addEventListener('DOMContentLoaded', () => {
    setupGlobalSelectorEventListeners(); // Assumes this function is available

    // Set up a MutationObserver to detect when new field/option containers are added to the DOM
    const observer = new MutationObserver((mutations) => {
        let selectorsMightNeedValidation = false;

        // Check if any mutations involve adding nodes that could contain selectors
        mutations.forEach(mutation => {
            if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                for (const node of mutation.addedNodes) {
                     if (node.nodeType === 1) { // Check if it's an element node
                         if (node.classList.contains('dynamic-field-container') || node.classList.contains('option-item') || node.querySelector('.field-selector, .option-selector')) {
                             selectorsMightNeedValidation = true;
                             break;
                         }
                     }
                }
            }
        });

        // If elements that might contain selectors were added, re-validate all
        if (selectorsMightNeedValidation) {
             // console.log("MutationObserver detected added nodes, re-validating selectors.");
             // Use a slight delay to ensure elements are fully rendered and attached
            setTimeout(() => {
                // Use our Promise-based validation
                ensureAllSelectorInputsAreValidated().then(() => {
                    // console.log("Re-validation complete after DOM mutation");
                    // Update all accordion header states after validation is complete
                    // Force update since this is in response to DOM mutations
                    if (typeof updateAllAccordionHeaderStates === 'function') {
                        updateAllAccordionHeaderStates(true); // Force update
                    }
                }).catch(error => {
                    console.error("Error during re-validation after DOM mutation:", error);
                });
            }, 100);
        }
    });

    // Start observing the main content area for changes
    // Adjust the target element if your dynamic content is added elsewhere
    const adminFieldsContentUI = document.getElementById('admin-form-fields-content');
     if (adminFieldsContentUI) {
         observer.observe(adminFieldsContentUI, {
             childList: true, // Observe direct children additions/removals
             subtree: true // Observe changes within the descendants
             // attributeFilter: ['class'] // Optional: only trigger for class changes if needed, but subtree/childList is usually enough
         });
         console.log("MutationObserver started on admin form fields container.");
     } else {
         console.warn("MutationObserver target element #admin-form-fields-content not found.");
         // As a fallback, observe the body if the target isn't found, but it's less efficient
         // observer.observe(document.body, { childList: true, subtree: true });
     }


    // Run initial validation once on load for existing inputs and wait for it to complete
    // The function now returns a Promise
    ensureAllSelectorInputsAreValidated().then(() => {
        console.log("Initial validation complete, updating all accordion header states");
        // Update all accordion header states after validation is complete
        // This is the initial load, so we'll let the flag mechanism handle it
        if (typeof updateAllAccordionHeaderStates === 'function') {
            updateAllAccordionHeaderStates(false); // Not forced, let the flag mechanism handle it
        }
    }).catch(error => {
        console.error("Error during initial validation:", error);
    });
});


console.log("sidepanel-ui-admin-events.js loaded");