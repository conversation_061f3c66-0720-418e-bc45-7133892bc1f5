// xpath-to-css.js
// Utility for converting XPath expressions to CSS selectors

console.log("xpath-to-css.js loading");

/**
 * Converts an XPath expression to a CSS selector
 * @param {string} xpath - The XPath expression to convert
 * @returns {string} The equivalent CSS selector
 */
function xpathToCssSelector(xpath) {
    // Handle empty input
    if (!xpath || xpath.trim() === '') {
        return '';
    }

    try {
        // Normalize the XPath by removing any leading or trailing whitespace
        xpath = xpath.trim();

        // Handle absolute XPath paths that start with /html
        if (xpath.startsWith('/html')) {
            return convertAbsoluteXPath(xpath);
        }
        
        // Handle other XPath formats (could be expanded in the future)
        return `/* XPath conversion not supported for this format: ${xpath} */`;
    } catch (error) {
        console.error('Error converting XPath to CSS:', error);
        return `/* Error converting XPath: ${error.message} */`;
    }
}

/**
 * Converts an absolute XPath expression to a CSS selector
 * @param {string} xpath - The absolute XPath expression
 * @returns {string} The equivalent CSS selector
 */
function convertAbsoluteXPath(xpath) {
    // Split the XPath into parts
    const parts = xpath.split('/').filter(part => part.trim() !== '');
    
    // Convert each part to a CSS selector
    const cssSelectors = parts.map(part => {
        // Check if the part has an index (e.g., div[3])
        const indexMatch = part.match(/([a-zA-Z0-9_-]+)(?:\[(\d+)\])?/);
        
        if (indexMatch) {
            const [, tagName, index] = indexMatch;
            
            // If there's an index, use nth-of-type
            if (index) {
                // XPath indices are 1-based, so we can use them directly in nth-of-type
                return `${tagName}:nth-of-type(${index})`;
            }
            
            // If there's no index, just use the tag name
            return tagName;
        }
        
        // If the part doesn't match the expected format, return it as is
        return part;
    });
    
    // Join the CSS selectors with the child combinator
    return cssSelectors.join(' > ');
}

// Make the function available globally
window.xpathToCssSelector = xpathToCssSelector;

console.log("xpath-to-css.js loaded");
