// content-highlight.js - Implements highlighting functionality for selectors

window.validatorHighlightedElement = window.validatorHighlightedElement || null;
window.validationHighlightTimeout = window.validationHighlightTimeout || null;
window.isValidationHighlightActive = window.isValidationHighlightActive || false;

function highlightElementBySelector(selector, isValid = true) {
    if (!selector || typeof selector !== 'string' || !selector.trim()) {
        removeValidationHighlight();
        return false;
    }

    try {
        // Find the element using the selector
        const element = document.querySelector(selector);

        if (!element) {
            removeValidationHighlight();
            return false;
        }

        // Use the existing highlightElement function from element-utils.js
        if (typeof window.highlightElement === 'function') {
            window.highlightElement(element, true, false);
            window.validatorHighlightedElement = element;
            window.isValidationHighlightActive = true;
            return true;
        } else {
            console.error('highlightElement function not available');
            return false;
        }
    } catch (error) {
        console.error(`Error highlighting element by selector: ${error.message}`);
        removeValidationHighlight();
        return false;
    }
}

function removeValidationHighlight() {

    if (typeof window.removeHighlight === 'function') {
        window.removeHighlight();
    }

    window.validatorHighlightedElement = null;
    window.isValidationHighlightActive = false;
}

function validateSelector(selector) {

    if (!selector || typeof selector !== 'string' || !selector.trim()) {
        return false;
    }

    try {
        // Try to find at least one element with the selector
        const element = document.querySelector(selector);
        return !!element;
    } catch (error) {
        console.error(`Invalid selector syntax: ${error.message}`);
        return false;
    }
}

function validateSelectorAndUpdateUI(selector, inputElement, containerElement) {
    const isValid = validateSelector(selector);

    return isValid;
}

function isElementInViewport(element) {
    const rect = element.getBoundingClientRect();
    return (
        rect.top >= 0 &&
        rect.left >= 0 &&
        rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
        rect.right <= (window.innerWidth || document.documentElement.clientWidth)
    );
}

// Array to store highlight divs for multiple elements
window.multiHighlightDivs = window.multiHighlightDivs || [];

// Function to highlight multiple elements by their selectors
function highlightMultipleElementsBySelectors(selectors) {
    if (!selectors || !Array.isArray(selectors) || selectors.length === 0) {
        removeAllHighlights();
        return false;
    }

    // First remove any existing highlights
    removeAllHighlights();

    // Track if at least one element was highlighted
    let atLeastOneHighlighted = false;

    // Process each selector
    selectors.forEach((selector, index) => {
        if (!selector || typeof selector !== 'string' || !selector.trim()) {
            return; // Skip empty selectors
        }

        try {
            // Find the element using the selector
            const element = document.querySelector(selector.trim());
            if (!element) {
                return; // Skip if element not found
            }

            // Create a highlight div for this element
            const highlightDiv = document.createElement('div');
            const highlightId = `form-analyzer-highlight-${index}`;
            highlightDiv.id = highlightId;
            highlightDiv.style.position = 'fixed';
            highlightDiv.style.zIndex = '2147483640';
            highlightDiv.style.pointerEvents = 'none';
            highlightDiv.style.boxSizing = 'border-box';
            highlightDiv.style.transition = 'all 0.1s ease-out';
            document.body.appendChild(highlightDiv);

            // Store the div in our array for later removal
            window.multiHighlightDivs.push(highlightDiv);

            // Get element position
            const rect = element.getBoundingClientRect();

            // Apply highlight styles (orange for hover)
            Object.assign(highlightDiv.style, {
                top: `${rect.top}px`,
                left: `${rect.left}px`,
                width: `${rect.width}px`,
                height: `${rect.height}px`,
                border: '2px solid rgba(255, 165, 0, 0.7)',
                backgroundColor: 'rgba(255, 165, 0, 0.1)',
                boxShadow: '0 0 0 2px rgba(255, 165, 0, 0.7)',
                opacity: '1',
                display: 'block',
                pointerEvents: 'none'
            });

            // Add a label with the selector
            const label = document.createElement('div');
            label.className = 'highlight-label';

            // Style the label - match the style from highlightElement in element-utils.js
            Object.assign(label.style, {
                position: 'absolute',
                top: `-${25 + (index * 3)}px`, // Stagger labels vertically (25px to match standard)
                left: `${index * 5}px`,        // Stagger labels horizontally
                backgroundColor: 'rgba(255, 165, 0, 0.9)',
                color: 'white',
                padding: '2px 6px',
                borderRadius: '3px',
                fontSize: '12px',
                fontFamily: 'monospace',
                fontWeight: 'bold',           // Add bold to match standard highlight
                whiteSpace: 'nowrap',
                maxWidth: '500px',            // Add max width to match standard highlight
                overflow: 'hidden',           // Add overflow handling to match standard highlight
                textOverflow: 'ellipsis',     // Add text overflow to match standard highlight
                pointerEvents: 'none',
                zIndex: `${2147483641 + index}` // Ensure proper stacking
            });

            // For hover, show the element tag and selector path
            // Try to use the same approach as the standard highlight
            const tagName = element.tagName.toLowerCase();

            // Try to use the generateNthChildSelectorStrict function if available
            let displaySelector;
            if (typeof window.generateNthChildSelectorStrict === 'function') {
                // Use the same function as the standard highlight
                displaySelector = window.generateNthChildSelectorStrict(element);
            } else {
                // Fallback to our own concise display logic
                displaySelector = selector.trim();

                // If the selector is too long, truncate it
                if (displaySelector.length > 40) {
                    // Try to extract just the last part of the selector (usually the most specific)
                    const parts = displaySelector.split(' > ');
                    if (parts.length > 1) {
                        // Take just the last part
                        displaySelector = '...' + parts[parts.length - 1];
                    } else {
                        // Or just truncate if it's a single part
                        displaySelector = displaySelector.substring(0, 37) + '...';
                    }
                }
            }

            label.textContent = `<${tagName}> ${displaySelector}`;

            // Add the label to the highlight div
            highlightDiv.appendChild(label);

            // Adjust label position if it would go off-screen
            setTimeout(() => {
                const labelRect = label.getBoundingClientRect();

                // Check if label is going off the left edge of the viewport
                if (labelRect.left < 0) {
                    label.style.left = `${Math.abs(labelRect.left) + 5}px`;
                }

                // Check if label is going off the top edge of the viewport
                if (labelRect.top < 0) {
                    // Move label to bottom of element instead
                    label.style.top = `${rect.height + 5}px`;
                    label.style.bottom = 'auto';
                }

                // Check if label is going off the right edge of the viewport
                const viewportWidth = window.innerWidth || document.documentElement.clientWidth;
                if (labelRect.right > viewportWidth) {
                    // Adjust to keep it within viewport
                    const overflow = labelRect.right - viewportWidth;
                    label.style.left = `${parseInt(label.style.left) - overflow - 10}px`;
                }
            }, 0);

            atLeastOneHighlighted = true;
        } catch (error) {
            console.error(`Error highlighting element by selector: ${error.message}`);
        }
    });

    return atLeastOneHighlighted;
}

// Function to remove all highlight divs
function removeAllHighlights() {
    // Remove all highlight divs from our array
    if (window.multiHighlightDivs && window.multiHighlightDivs.length > 0) {
        window.multiHighlightDivs.forEach(div => {
            if (div && div.parentNode) {
                div.parentNode.removeChild(div);
            }
        });
        window.multiHighlightDivs = [];
    }

    // Also remove the single highlight if it exists
    removeValidationHighlight();
}

// Export functions to the window object to make them available to the content script
window.highlightElementBySelector = highlightElementBySelector;
window.removeValidationHighlight = removeValidationHighlight;
window.validateSelector = validateSelector;
window.validateSelectorAndUpdateUI = validateSelectorAndUpdateUI;
window.isElementInViewport = isElementInViewport;
window.highlightMultipleElementsBySelectors = highlightMultipleElementsBySelectors;
window.removeAllHighlights = removeAllHighlights;

