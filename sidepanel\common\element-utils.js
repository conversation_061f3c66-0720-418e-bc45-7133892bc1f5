// Use window properties to avoid redeclaration errors when loaded multiple times
window.lastSelectedElement = window.lastSelectedElement || null;
window.selectionModeActive = window.selectionModeActive || false;
window.currentHighlightElement = window.currentHighlightElement || null;
window.clickStartTarget = window.clickStartTarget || null; // Track where the mousedown event started

function generateNthChildSelectorStrict(element) {
  if (!element || element.nodeType !== Node.ELEMENT_NODE) {
    return null;
  }
  const path = [];
  let currentElement = element;
  while (currentElement && currentElement.nodeType === Node.ELEMENT_NODE) {
    const parent = currentElement.parentElement;
    if (!parent || currentElement === document.documentElement) {
      path.unshift(currentElement.tagName.toLowerCase());
      break;
    }
    let index = 1;
    let sibling = currentElement.previousElementSibling;
    while (sibling) {
      if (sibling.tagName === currentElement.tagName) {
          index++;
      }
      sibling = sibling.previousElementSibling;
    }
    const selectorPart = `${currentElement.tagName.toLowerCase()}:nth-of-type(${index})`;
    path.unshift(selectorPart);
    currentElement = parent;
  }
  return path.join(' > ');
}

function getElementInfo(element) {
  if (!element) return null;
  try {
    const tagName = element.tagName.toLowerCase();
    const id = element.id ? '#' + element.id : '';
    const classes = Array.from(element.classList).map(c => '.' + c).join('');

    let valueInfo = null;
    let elementType = tagName;

    if (tagName === 'input') {
        elementType = `input-${element.type || 'text'}`;
        valueInfo = { currentValue: element.value, defaultValue: element.defaultValue };
        if (element.type === 'checkbox' || element.type === 'radio') {
             valueInfo.checked = element.checked;
             valueInfo.valueAttribute = element.getAttribute('value');
        }
    } else if (tagName === 'textarea') {
        elementType = 'textarea';
        valueInfo = { currentValue: element.value, defaultValue: element.defaultValue };
    } else if (tagName === 'select') {
        elementType = 'select';
        const selectedOption = element.options[element.selectedIndex];
        valueInfo = {
            currentValue: element.value,
            selectedIndex: element.selectedIndex,
            selectedText: selectedOption ? selectedOption.text : null,
            options: Array.from(element.options).map(opt => ({ value: opt.value, text: opt.text }))
        };
    } else if (element.value !== undefined) {
        valueInfo = { currentValue: element.value };
    }

    const textContent = element.textContent?.trim() || '';
    const innerHTML = element.innerHTML;
    const outerHTML = element.outerHTML;

    const attributes = {};
    Array.from(element.attributes).forEach(attr => {
        attributes[attr.name] = attr.value;
    });

    const computedStyle = window.getComputedStyle(element);
    const styles = {
      display: computedStyle.display,
      visibility: computedStyle.visibility,
      position: computedStyle.position,
      width: computedStyle.width,
      height: computedStyle.height,
    };

    const rect = element.getBoundingClientRect();
    const position = { x: rect.left, y: rect.top, width: rect.width, height: rect.height };

    const accessibility = {
      role: element.getAttribute('role'),
      ariaLabel: element.getAttribute('aria-label'),
      ariaLabelledBy: element.getAttribute('aria-labelledby'),
      ariaDescribedBy: element.getAttribute('aria-describedby'),
      tabIndex: element.tabIndex,
    };

     let cssSelectorSimple = tagName;
     if (element.id) {
         cssSelectorSimple = `#${element.id}`;
     } else if (element.classList.length > 0) {
         cssSelectorSimple += `.${Array.from(element.classList).join('.')}`;
     }
     const absolutePath = generateNthChildSelectorStrict(element);

    let formInfo = {};
    const isForm = tagName === 'form';
    const isInput = ['input', 'textarea', 'select', 'button'].includes(tagName);
    if (isForm) {
        formInfo = { action: element.action || '', method: element.method || 'get', fieldCount: element.elements.length };
    } else if (isInput) {
        formInfo = { name: element.name || '', formId: element.form?.id || null, required: element.required };
        if (element.placeholder) formInfo.placeholder = element.placeholder;
        if (element.type) formInfo.typeAttribute = element.type;
    }

    return {
      tagName: tagName,
      elementType: elementType,
      id: element.id || null,
      classList: Array.from(element.classList),
      valueInfo: valueInfo,
      textContent: textContent,
      innerHTML: innerHTML,
      outerHTML: outerHTML,
      attributes: attributes,
      styles: styles,
      position: position,
      accessibility: accessibility,
      cssSelectorSimple: cssSelectorSimple,
      absolutePath: absolutePath,
      formInfo: formInfo,
      isForm: isForm,
      isInput: isInput,
    };
  } catch (e) {
    console.error('Error in getElementInfo:', e);
    return { error: e.message, tagName: element?.tagName };
  }
}

function highlightElement(element, isHover = false, isSelected = false) {
    if (!element) {
        return null;
    }

    const HIGHLIGHT_ID = 'form-analyzer-highlight';
    let highlightDiv = document.getElementById(HIGHLIGHT_ID);

    if (!highlightDiv) {
        highlightDiv = document.createElement('div');
        highlightDiv.id = HIGHLIGHT_ID;
        highlightDiv.style.position = 'fixed';
        highlightDiv.style.zIndex = '2147483640';
        highlightDiv.style.pointerEvents = 'none';
        highlightDiv.style.boxSizing = 'border-box';
        highlightDiv.style.transition = 'all 0.1s ease-out';
        document.body.appendChild(highlightDiv);
    } else {
        // Make sure the highlight div is visible
        highlightDiv.style.display = 'block';
        highlightDiv.style.opacity = '1';
    }

    const rect = element.getBoundingClientRect();

    // Define styles based on the state
    let borderStyle, bgColor, boxShadow;

    if (isSelected) {
        // Selected state (green)
        borderStyle = '3px solid #4CAF50';
        bgColor = 'rgba(76, 175, 80, 0.2)';
        boxShadow = '0 0 0 3px rgba(76, 175, 80, 0.5)';
    } else if (isHover) {
        // Hover state (orange)
        borderStyle = '2px solid rgba(255, 165, 0, 0.7)';
        bgColor = 'rgba(255, 165, 0, 0.1)';
        boxShadow = '0 0 0 2px rgba(255, 165, 0, 0.7)';
    } else {
        // Processing state (blue)
        borderStyle = '3px dashed #4285f4';
        bgColor = 'rgba(66, 133, 244, 0.1)';
        boxShadow = '0 0 0 3px rgba(66, 133, 244, 0.5)';
    }

    // Apply styles
    Object.assign(highlightDiv.style, {
        top: `${rect.top}px`,
        left: `${rect.left}px`,
        width: `${rect.width}px`,
        height: `${rect.height}px`,
        border: borderStyle,
        backgroundColor: bgColor,
        boxShadow: boxShadow,
        opacity: '1',
        display: 'block', // Ensure it's visible
        pointerEvents: 'none' // Make sure it doesn't interfere with mouse events
    });

    // Add a label to show what's happening
    if (!highlightDiv.querySelector('.highlight-label')) {
        const label = document.createElement('div');
        label.className = 'highlight-label';
        Object.assign(label.style, {
            position: 'absolute',
            top: '-25px',
            left: '0',
            backgroundColor: isHover ? 'rgba(255, 165, 0, 0.9)' : (isSelected ? 'rgba(76, 175, 80, 0.9)' : 'rgba(66, 133, 244, 0.9)'),
            color: 'white',
            padding: '2px 6px',
            borderRadius: '3px',
            fontSize: '12px',
            fontWeight: 'bold',
            whiteSpace: 'nowrap',
            maxWidth: '500px',
            overflow: 'hidden',
            textOverflow: 'ellipsis'
        });

        // Create label text based on state
        let labelText;
        if (isHover) {
            // For hover, show the element tag and selector path
            const tagName = element.tagName.toLowerCase();
            const selectorPath = generateNthChildSelectorStrict(element);
            labelText = `<${tagName}> ${selectorPath}`;
        } else if (isSelected) {
            labelText = 'Selected!';
        } else {
            labelText = 'Processing...';
        }

        label.textContent = labelText;
        highlightDiv.appendChild(label);
    } else {
        const label = highlightDiv.querySelector('.highlight-label');

        // Update label text based on state
        let labelText;
        if (isHover) {
            // For hover, show the element tag and selector path
            const tagName = element.tagName.toLowerCase();
            const selectorPath = generateNthChildSelectorStrict(element);
            labelText = `<${tagName}> ${selectorPath}`;
        } else if (isSelected) {
            labelText = 'Selected!';
        } else {
            labelText = 'Processing...';
        }

        label.textContent = labelText;
        label.style.backgroundColor = isHover ? 'rgba(255, 165, 0, 0.9)' : (isSelected ? 'rgba(76, 175, 80, 0.9)' : 'rgba(66, 133, 244, 0.9)');
    }

    return highlightDiv;
}

function removeHighlight() {
     const highlightDiv = document.getElementById('form-analyzer-highlight');

     if (highlightDiv) {
         // Just hide it instead of removing it completely
         highlightDiv.style.opacity = '0';
         highlightDiv.style.display = 'none';
     }
}

function enableSelectionMode(isSelectorGenerationOnly = false) {
  if (window.selectionModeActive) {
      return;
  }

  // Set the global flag to indicate whether this is for selector generation only
  window.isSelectorGenerationMode = isSelectorGenerationOnly;

  window.selectionModeActive = true;

  // Make sure any existing highlight is removed
  const existingHighlight = document.getElementById('form-analyzer-highlight');
  if (existingHighlight) {
      existingHighlight.remove();
  }

  // Reset state variables
  window.clickStartTarget = null;
  window.currentHighlightElement = null;

  // Create message indicator
  const messageDiv = document.createElement('div');
  messageDiv.id = 'form-analyzer-selection-message';
  messageDiv.textContent = 'Click element to select (Press ESC to cancel)';
  Object.assign(messageDiv.style, {
    position: 'fixed', top: '10px', left: '50%', transform: 'translateX(-50%)',
    backgroundColor: 'rgba(0,0,0,0.7)', color: 'white', padding: '8px 15px',
    borderRadius: '4px', zIndex: '2147483647', fontSize: '14px',
    boxShadow: '0 2px 5px rgba(0,0,0,0.3)'
  });
  document.body.appendChild(messageDiv);

  // Add event listeners for the new mousedown/mouseup approach
  document.addEventListener('mouseover', handleMouseOver, true);
  document.addEventListener('mouseout', handleMouseOut, true);
  document.addEventListener('mousedown', handleMouseDown, true);
  document.addEventListener('mouseup', handleMouseUp, true);
  document.addEventListener('keydown', handleEscapeKey, true);

  // Prevent normal clicks during selection mode
  document.addEventListener('click', preventDefaultClick, true);

  // Focus on the document body to ensure keyboard events are captured here
  document.body.focus();
}

function disableSelectionMode(reason = 'completed') {
    if (!window.selectionModeActive) {
        return;
    }
    window.selectionModeActive = false;

    // Remove UI elements
    const messageDiv = document.getElementById('form-analyzer-selection-message');
    if (messageDiv) messageDiv.remove();
    removeHighlight();

    // Reset state variables
    window.currentHighlightElement = null;
    window.clickStartTarget = null;

    // Remove all event listeners
    document.removeEventListener('mouseover', handleMouseOver, true);
    document.removeEventListener('mouseout', handleMouseOut, true);
    document.removeEventListener('mousedown', handleMouseDown, true);
    document.removeEventListener('mouseup', handleMouseUp, true);
    document.removeEventListener('click', preventDefaultClick, true);
    document.removeEventListener('keydown', handleEscapeKey, true);

    // Notify that selection is complete
    window.postMessage({
        action: 'selectionCompleted',
        source: 'form-analyzer-element-utils',
        reason: reason
    }, window.location.origin);
}

// Prevent any normal clicks during selection mode
function preventDefaultClick(e) {
    if (window.selectionModeActive) {
        e.preventDefault();
        e.stopPropagation();
        return false;
    }
}

// Handle mouse hover to show highlight
function handleMouseOver(e) {
    // Skip if selection mode is not active
    if (!window.selectionModeActive) {
        return;
    }

    // Skip our UI elements
    if (e.target.id === 'form-analyzer-selection-message' || e.target.id === 'form-analyzer-highlight' ||
        e.target.closest('#form-analyzer-highlight') || e.target.closest('#form-analyzer-selection-message')) {
        return;
    }

    // Skip if we're in the middle of a click (mousedown already happened)
    if (window.clickStartTarget) {
        return;
    }

    // If we're already highlighting this element, no need to recreate the highlight
    if (e.target === window.currentHighlightElement &&
        document.getElementById('form-analyzer-highlight')?.style.display !== 'none') {
        return;
    }

    // Update the current highlighted element
    window.currentHighlightElement = e.target;

    // Create a new highlight with hover styling
    highlightElement(e.target, true, false);
}

// Handle mouse out to maintain highlight
function handleMouseOut(e) {
    // If we're not in selection mode or we're in the middle of a click, do nothing
    if (!window.selectionModeActive || window.clickStartTarget) return;

    // If we're moving out from the current highlighted element to a non-highlightable element
    // (like the highlight itself or the message), keep the highlight
    if (e.target === window.currentHighlightElement &&
        (e.relatedTarget?.id === 'form-analyzer-highlight' ||
         e.relatedTarget?.id === 'form-analyzer-selection-message' ||
         e.relatedTarget?.closest('#form-analyzer-highlight') ||
         e.relatedTarget?.closest('#form-analyzer-selection-message'))) {
        return;
    }

    // If we're moving to another element that should be highlighted, let handleMouseOver handle it
    // This prevents flickering when moving between elements
    if (e.relatedTarget &&
        e.relatedTarget.id !== 'form-analyzer-highlight' &&
        e.relatedTarget.id !== 'form-analyzer-selection-message' &&
        !e.relatedTarget.closest('#form-analyzer-highlight') &&
        !e.relatedTarget.closest('#form-analyzer-selection-message')) {
        return;
    }

    // If we're moving out to nothing or to the document, hide the highlight
    if (!e.relatedTarget || e.relatedTarget.tagName === 'HTML' || e.relatedTarget.tagName === 'BODY') {
        removeHighlight();
        window.currentHighlightElement = null;
    }
}

// Track where the mouse button was pressed down
function handleMouseDown(e) {
    if (!window.selectionModeActive) return;
    if (e.target.id === 'form-analyzer-selection-message' || e.target.id === 'form-analyzer-highlight') return;

    // Store the exact element where mousedown occurred
    window.clickStartTarget = e.target;

    // Prevent default browser behavior
    e.preventDefault();
    e.stopPropagation();
    return false;
}

// Handle the mouse button release to complete selection
function handleMouseUp(e) {
    if (!window.selectionModeActive) return;
    if (e.target.id === 'form-analyzer-selection-message' || e.target.id === 'form-analyzer-highlight') return;

    // Prevent default browser behavior
    e.preventDefault();
    e.stopPropagation();

    // Only process if we have a valid mousedown target
    if (!window.clickStartTarget) return false;

    // Store the target locally to avoid race conditions
    const selectedElement = window.clickStartTarget;

    // Clear the current highlight to provide visual feedback
    removeHighlight();

    // Show a "processing" highlight on the selected element
    highlightElement(selectedElement, false, false);

    // Add a small delay to ensure we're not capturing accidental clicks
    // This also helps prevent double-processing
    setTimeout(() => {
        // Make sure selection mode is still active (user might have pressed ESC during the delay)
        if (!window.selectionModeActive) return;

        // Store as the last selected element for future reference
        window.lastSelectedElement = selectedElement;

        // Get detailed info about the selected element
        const elementInfo = getElementInfo(selectedElement);
        console.log('Element selected:', elementInfo);

        // Send the element info to the extension
        // Include a flag indicating whether this is for selector generation only
        window.postMessage({
            action: 'elementSelected',
            source: 'form-analyzer-element-utils',
            elementInfo: elementInfo,
            isSelectorGenerationOnly: window.isSelectorGenerationMode === true
        }, window.location.origin);

        // Show a success highlight
        removeHighlight();
        highlightElement(selectedElement, false, true);

        // Keep the highlight visible briefly for confirmation
        setTimeout(() => removeHighlight(), 1000);

        // Disable selection mode
        disableSelectionMode('completed');
    }, 50); // Short delay to ensure intentional selection

    return false;
}

function handleEscapeKey(e) {
    // Check if the key pressed is Escape and selection mode is active
    if (e.key === 'Escape' && window.selectionModeActive) {

        // Prevent default browser behavior and stop event propagation
        e.preventDefault();
        e.stopPropagation();

        // Clear any existing highlight
        removeHighlight();

        // Reset all state variables
        window.currentHighlightElement = null;
        window.clickStartTarget = null;

        // Disable selection mode with 'canceled' reason
        disableSelectionMode('canceled');
    }
}

function getLastSelectedElement() {
  return window.lastSelectedElement;
}

window.generateNthChildSelectorStrict = generateNthChildSelectorStrict;
window.getElementInfo = getElementInfo;
window.highlightElement = highlightElement;
window.removeHighlight = removeHighlight;
window.enableSelectionMode = enableSelectionMode;
window.disableSelectionMode = disableSelectionMode;
window.getLastSelectedElement = getLastSelectedElement;
window.isSelectionModeActive = () => window.selectionModeActive;

console.log('element-utils.js loaded (v3 - improved selection highlighting)');