// File: admin/sidepanel/sidepanel-ui-admin-selector.js

/**
 * Updates the accordion container class based on the state of its selector
 * @param {HTMLElement} selectorInput - The selector input element
 */
function updateAccordionHeaderState(selectorInput) {
    // Function modified to not add validation classes
    if (!selectorInput) return;

    // Find the parent accordion container
    const container = selectorInput.closest('.dynamic-field-container, .option-item');
    if (!container) return;

    // Remove all validation state classes
    container.classList.remove('has-valid-selector', 'has-invalid-selector', 'has-missing-selector');

    // No longer adding validation classes to containers
    // console.log('Removed validation classes from container:', container);
}

/**
 * Updates all accordion headers based on their selector states
 * Optimized to process one container only once
 */
// Add a flag to track if we've already updated the accordion headers during initialization
let accordionHeadersUpdatedOnLoad = false;

function updateAllAccordionHeaderStates(forceUpdate = false) {
    // If we've already updated the headers during initialization and this isn't a forced update, skip
    if (accordionHeadersUpdatedOnLoad && !forceUpdate) {
        console.log('Accordion headers already updated during initialization, skipping');
        return;
    }

    // console.log('Updating all accordion header states');

    // Keep track of processed containers to avoid duplicate processing
    const processedContainers = new Set();

    // Process field selectors
    const fieldSelectors = document.querySelectorAll('.field-selector');
    // console.log(`Found ${fieldSelectors.length} field selectors`);

    fieldSelectors.forEach(selectorInput => {
        const container = selectorInput.closest('.dynamic-field-container');
        if (container && !processedContainers.has(container)) {
            updateAccordionHeaderState(selectorInput);
            processedContainers.add(container);
        }
    });

    // Process option selectors
    const optionSelectors = document.querySelectorAll('.option-selector');
    // console.log(`Found ${optionSelectors.length} option selectors`);

    optionSelectors.forEach(selectorInput => {
        const container = selectorInput.closest('.option-item');
        if (container && !processedContainers.has(container)) {
            updateAccordionHeaderState(selectorInput);
            processedContainers.add(container);
        }
    });

    // console.log(`Updated ${processedContainers.size} unique containers`);

    // Set the flag to indicate we've updated the headers during initialization
    accordionHeadersUpdatedOnLoad = true;
}

/**
 * Highlights an element based on its selector (without validation)
 * @param {HTMLElement} selectorInput - The selector input element
 * @param {boolean} shouldHighlight - Whether to highlight the element
 * @returns {Promise} - A promise that resolves when the operation is complete
 */
async function highlightSelectorElement(selectorInput, shouldHighlight = false) {
    if (!selectorInput || !state.currentTabId) return Promise.resolve();

    const selector = selectorInput.value.trim();

    // Remove all validation classes regardless of selector value
    selectorInput.classList.remove('valid-selector', 'invalid-selector', 'missing-selector');

    // Update the accordion header state to remove validation classes
    updateAccordionHeaderState(selectorInput);

    if (!selector) {
        // Remove any existing highlight if we have a valid tab
        if (state.currentTabId) {
            try {
                await chrome.runtime.sendMessage({
                    action: 'proxyRequestToContentScript',
                    targetTabId: state.currentTabId,
                    contentScriptAction: 'removeValidationHighlight',
                    payload: {}
                }).catch(() => {
                    console.log('Note: Could not remove highlight - tab may be loading or not accessible');
                });
            } catch (error) {
                console.log('Note: Error in removeValidationHighlight - tab may be loading or not accessible');
            }
        }
        return Promise.resolve();
    }

    try {
        // Only handle highlighting if requested
        if (shouldHighlight) {
            await chrome.runtime.sendMessage({
                action: 'proxyRequestToContentScript',
                targetTabId: state.currentTabId,
                contentScriptAction: 'highlightElementBySelector',
                payload: { selector: selector, isValid: true }
            }).catch(() => {
                console.log('Note: Could not highlight element - tab may be loading or not accessible');
            });
        } else {
            // Remove any existing highlight if we're not supposed to highlight
            if (state.currentTabId) {
                try {
                    await chrome.runtime.sendMessage({
                        action: 'proxyRequestToContentScript',
                        targetTabId: state.currentTabId,
                        contentScriptAction: 'removeValidationHighlight',
                        payload: {}
                    }).catch(() => {
                        console.log('Note: Could not remove highlight - tab may be loading or not accessible');
                    });
                } catch (error) {
                    console.log('Note: Error in removeValidationHighlight - tab may be loading or not accessible');
                }
            }
        }
    } catch (error) {
        console.error('Error handling selector highlight:', error);
    }

    return Promise.resolve();
}

// Create debounced version of the highlight function
const debouncedHighlightSelector = debounce(highlightSelectorElement, 300);

console.log("sidepanel-ui-admin-selector.js loaded");