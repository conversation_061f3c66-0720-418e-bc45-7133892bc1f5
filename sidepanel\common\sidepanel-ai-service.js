// sidepanel-ai-service.js
// Handles AI service calls (OpenAI and Gemini)

console.log("sidepanel-ai-service.js loading");

// API Keys and endpoints
const OPENAI_API_URL = "https://api.openai.com/v1/chat/completions";
const OPENAI_API_KEY = "********************************************************************************************************************************************************************";
const OPENAI_MODEL = "gpt-4o-mini";

const GEMINI_API_URL = "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent";
const GEMINI_API_KEY = "AIzaSyBakqvHdIh3Uv0j2-qllwD2Q1ld77swhQI";
const GEMINI_MODEL = "gemini-2.0-flash";

// Get the current AI model preference from storage
async function getCurrentAIModel() {
    try {
        const result = await new Promise(resolve => chrome.storage.local.get(['aiModel'], resolve));
        return result?.aiModel || 'openai'; // Default to OpenAI if not set
    } catch (error) {
        console.error("AI-Service: Error getting AI model preference:", error);
        return 'openai'; // Default to OpenAI on error
    }
}

// Call the appropriate AI service based on user preference
async function callAIForFormJSON(selectedHTML) {
    const aiModel = await getCurrentAIModel();
    console.log(`AI-Service: Using ${aiModel} for form JSON generation`);

    // Get available report field names from state
    const reportFieldNames = Object.keys(state.reportFieldValues || {}).sort();
    console.log(`AI-Service: Available report field names:`, reportFieldNames);

    if (aiModel === 'gemini') {
        return callGeminiForFormJSON(selectedHTML, reportFieldNames);
    } else {
        return callOpenAIForFormJSON(selectedHTML, reportFieldNames);
    }
}

// Call OpenAI API for form field analysis
async function callOpenAIForFormJSON(selectedHTML, reportFieldNames = []) {
    const MAX_HTML_LENGTH = 100000;
    let processedHTML = selectedHTML;
    if (processedHTML.length > MAX_HTML_LENGTH) {
        console.warn(`AI-Service: OpenAI HTML input truncated: ${processedHTML.length} -> ${MAX_HTML_LENGTH}`);
        processedHTML = processedHTML.substring(0, MAX_HTML_LENGTH);
    }
    const prompt = getFormAnalysisPrompt(processedHTML, reportFieldNames);

    console.log("AI-Service: Sending request to OpenAI...");
    if (!OPENAI_API_KEY || OPENAI_API_KEY.includes("YOUR_API_KEY")) {
         console.error("OpenAI API Key not configured in sidepanel-ai-service.js");
         alert("OpenAI API Key is missing. Please configure it in the extension code.");
         return [];
    }

    try {
        const response = await fetch(OPENAI_API_URL, {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
                "Authorization": `Bearer ${OPENAI_API_KEY}`
            },
            body: JSON.stringify({
                model: OPENAI_MODEL,
                messages: [
                    { "role": "system", "content": "You are an HTML form analyzer. Output ONLY a valid JSON array based on the user's instructions." },
                    { "role": "user", "content": prompt }
                ],
                temperature: 0.1,
                max_tokens: 3000,
            })
        });

        if (!response.ok) {
             const errorBody = await response.text();
             console.error("OpenAI API Error Body:", errorBody);
             throw new Error(`OpenAI API Error: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();
        console.log("AI-Service: OpenAI response received.");

        if (!data.choices?.[0]?.message?.content) {
            throw new Error("No response content from OpenAI.");
        }

        let content = data.choices[0].message.content.trim();
        content = content.replace(/^```json\s*/, '').replace(/\s*```$/, '').trim();

        const jsonResult = JSON.parse(content);

        if (!Array.isArray(jsonResult)) {
            throw new Error("OpenAI response was not a valid JSON array.");
        }

        console.log("AI-Service: Parsed OpenAI JSON:", jsonResult);
        return jsonResult;

    } catch (error) {
        console.error("AI-Service: Error calling OpenAI or parsing response:", error);
        alert(`OpenAI Error: ${error.message}`);
        return [];
    }
}

// Call Gemini API for form field analysis
async function callGeminiForFormJSON(selectedHTML, reportFieldNames = []) {
    const MAX_HTML_LENGTH = 100000;
    let processedHTML = selectedHTML;
    if (processedHTML.length > MAX_HTML_LENGTH) {
        console.warn(`AI-Service: Gemini HTML input truncated: ${processedHTML.length} -> ${MAX_HTML_LENGTH}`);
        processedHTML = processedHTML.substring(0, MAX_HTML_LENGTH);
    }
    const prompt = getFormAnalysisPrompt(processedHTML, reportFieldNames);

    console.log("AI-Service: Sending request to Gemini...");
    if (!GEMINI_API_KEY || GEMINI_API_KEY.includes("YOUR_API_KEY")) {
         console.error("Gemini API Key not configured in sidepanel-ai-service.js");
         alert("Gemini API Key is missing. Please configure it in the extension code.");
         return [];
    }

    try {
        const response = await fetch(`${GEMINI_API_URL}?key=${GEMINI_API_KEY}`, {
            method: "POST",
            headers: {
                "Content-Type": "application/json"
            },
            body: JSON.stringify({
                contents: [
                    {
                        parts: [
                            {
                                text: "You are an HTML form analyzer. Output ONLY a valid JSON array based on the following instructions."
                            },
                            {
                                text: prompt
                            }
                        ]
                    }
                ],
                generationConfig: {
                    temperature: 0.1,
                    maxOutputTokens: 3000,
                    topP: 0.95,
                    topK: 40
                }
            })
        });

        if (!response.ok) {
             const errorBody = await response.text();
             console.error("Gemini API Error Body:", errorBody);
             throw new Error(`Gemini API Error: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();
        console.log("AI-Service: Gemini response received:", data);

        if (!data.candidates?.[0]?.content?.parts?.[0]?.text) {
            throw new Error("No response content from Gemini.");
        }

        let content = data.candidates[0].content.parts[0].text.trim();
        content = content.replace(/^```json\s*/, '').replace(/\s*```$/, '').trim();

        const jsonResult = JSON.parse(content);

        if (!Array.isArray(jsonResult)) {
            throw new Error("Gemini response was not a valid JSON array.");
        }

        console.log("AI-Service: Parsed Gemini JSON:", jsonResult);
        return jsonResult;

    } catch (error) {
        console.error("AI-Service: Error calling Gemini or parsing response:", error);
        alert(`Gemini Error: ${error.message}`);
        return [];
    }
}

// Simple fallback matching function
function findBestMatchingOptionFallback(options, targetValue, requestId = 'unknown') {
    console.log(`AI-Service [${requestId}]: Using fallback matching for value: "${targetValue}"`);

    const targetLower = targetValue.toLowerCase();

    // Try partial matches
    const partialMatches = options.filter(opt => {
        const textLower = opt.text.toLowerCase();
        return textLower.includes(targetLower) || targetLower.includes(textLower);
    });

    if (partialMatches.length > 0) {
        // Return the first partial match
        const match = partialMatches[0];
        console.log(`AI-Service [${requestId}]: Found fallback partial match:`, match);
        return { ...match, confidence: 50, matchType: "fallback_partial" };
    }

    // Try word-based matching
    const targetWords = targetLower.split(/\s+/);
    const wordMatches = options.filter(opt => {
        const textWords = opt.text.toLowerCase().split(/\s+/);
        return targetWords.some(targetWord =>
            textWords.some(textWord =>
                targetWord.length > 2 && textWord.includes(targetWord)
            )
        );
    });

    if (wordMatches.length > 0) {
        const match = wordMatches[0];
        console.log(`AI-Service [${requestId}]: Found fallback word match:`, match);
        return { ...match, confidence: 30, matchType: "fallback_word" };
    }

    console.log(`AI-Service [${requestId}]: No fallback match found`);
    return null;
}

// Find best matching option using AI
async function findBestMatchingOption(options, targetValue, fieldLabel = '') {
    // Add a unique request ID to track this specific request through the logs
    const requestId = Math.random().toString(36).substring(2, 10);

    if (!options || !options.length || !targetValue) {
        console.error(`AI-Service [${requestId}]: Invalid options or target value for matching.`);
        return null;
    }

    try {
        console.log(`AI-Service [${requestId}]: Finding best matching option for value:`, targetValue);
        if (fieldLabel) {
            console.log(`AI-Service [${requestId}]: Using field label for context:`, fieldLabel);
        }

        const exactMatch = options.find(opt =>
            opt.value === targetValue ||
            opt.text === targetValue ||
            opt.text.toLowerCase() === targetValue.toLowerCase()
        );

        if (exactMatch) {
            console.log(`AI-Service [${requestId}]: Found exact match:`, exactMatch);
            return exactMatch;
        }

        // Use the appropriate AI model based on user preference
        const aiModel = await getCurrentAIModel();
        console.log(`AI-Service [${requestId}]: Using ${aiModel} model for option matching`);

        let aiResult = null;
        try {
            if (aiModel === 'gemini') {
                aiResult = await findBestMatchingOptionWithGemini(options, targetValue, requestId, fieldLabel);
            } else {
                aiResult = await findBestMatchingOptionWithOpenAI(options, targetValue, requestId, fieldLabel);
            }
        } catch (aiError) {
            console.warn(`AI-Service [${requestId}]: AI matching failed, will try fallback:`, aiError);
        }

        // If AI matching succeeded, return the result
        if (aiResult) {
            return aiResult;
        }

        // If AI matching failed, try fallback matching
        console.log(`AI-Service [${requestId}]: AI matching failed, trying fallback matching...`);
        return findBestMatchingOptionFallback(options, targetValue, requestId);

    } catch (error) {
        console.error(`AI-Service [${requestId}]: Error finding best matching option:`, error);

        // Try fallback matching even if there was an error
        try {
            return findBestMatchingOptionFallback(options, targetValue, requestId);
        } catch (fallbackError) {
            console.error(`AI-Service [${requestId}]: Fallback matching also failed:`, fallbackError);
            return null;
        }
    }
}

// Find best matching option using OpenAI
async function findBestMatchingOptionWithOpenAI(options, targetValue, requestId = 'unknown', fieldLabel = '') {
    const prompt = getOptionMatchingPrompt(options, targetValue, fieldLabel);

    console.log(`AI-Service [${requestId}]: Sending request to OpenAI for option matching...`);
    if (!OPENAI_API_KEY || OPENAI_API_KEY.includes("YOUR_API_KEY")) {
        console.error(`AI-Service [${requestId}]: OpenAI API Key not configured in sidepanel-ai-service.js`);
        return null;
    }

    try {
        const response = await fetch(OPENAI_API_URL, {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
                "Authorization": `Bearer ${OPENAI_API_KEY}`
            },
            body: JSON.stringify({
                model: OPENAI_MODEL,
                messages: [
                    { role: "system", content: "You are a specialized AI assistant that helps match form field values to the closest option in a dropdown menu. You analyze both direct matches and category/parent group matches when direct matches aren't available." },
                    { role: "user", content: prompt }
                ],
                temperature: 0.1,
                max_tokens: 150,
                response_format: { type: "json_object" }
            })
        });

        if (!response.ok) {
            const errorBody = await response.text();
            console.error("OpenAI API Error Body:", errorBody);
            throw new Error(`OpenAI API Error: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();
        console.log(`AI-Service [${requestId}]: OpenAI response received for option matching.`);

        if (!data.choices?.[0]?.message?.content) {
            throw new Error(`AI-Service [${requestId}]: No response content from OpenAI for option matching.`);
        }

        const aiResponse = data.choices[0].message.content.trim();
        return processAIOptionMatchResponse(aiResponse, options, requestId);
    } catch (error) {
        console.error(`AI-Service [${requestId}]: Error with OpenAI option matching:`, error);
        return null;
    }
}

// Find best matching option using Gemini
async function findBestMatchingOptionWithGemini(options, targetValue, requestId = 'unknown', fieldLabel = '') {
    const prompt = getOptionMatchingPrompt(options, targetValue, fieldLabel);

    console.log(`AI-Service [${requestId}]: Sending request to Gemini for option matching...`);
    if (!GEMINI_API_KEY || GEMINI_API_KEY.includes("YOUR_API_KEY")) {
        console.error(`AI-Service [${requestId}]: Gemini API Key not configured in sidepanel-ai-service.js`);
        return null;
    }

    try {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout

        const response = await fetch(`${GEMINI_API_URL}?key=${GEMINI_API_KEY}`, {
            method: "POST",
            headers: {
                "Content-Type": "application/json"
            },
            body: JSON.stringify({
                contents: [
                    {
                        parts: [
                            {
                                text: "You are a specialized AI assistant that helps match form field values to the closest option in a dropdown menu. You analyze both direct matches and category/parent group matches when direct matches aren't available."
                            },
                            {
                                text: prompt
                            }
                        ]
                    }
                ],
                generationConfig: {
                    temperature: 0.1,
                    maxOutputTokens: 150,
                    topP: 0.95,
                    topK: 40
                }
            }),
            signal: controller.signal
        });

        clearTimeout(timeoutId);

        if (!response.ok) {
            const errorBody = await response.text();
            console.error(`AI-Service [${requestId}]: Gemini API Error Body:`, errorBody);

            // Check for specific error types
            if (response.status === 429) {
                console.warn(`AI-Service [${requestId}]: Gemini API rate limit exceeded`);
                throw new Error(`Rate limit exceeded. Please try again later.`);
            } else if (response.status === 403) {
                console.warn(`AI-Service [${requestId}]: Gemini API access forbidden`);
                throw new Error(`API access forbidden. Check your API key.`);
            } else if (response.status >= 500) {
                console.warn(`AI-Service [${requestId}]: Gemini API server error`);
                throw new Error(`Server error. Please try again later.`);
            }

            throw new Error(`Gemini API Error: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();
        console.log(`AI-Service [${requestId}]: Gemini response received for option matching.`);

        if (!data.candidates?.[0]?.content?.parts?.[0]?.text) {
            console.warn(`AI-Service [${requestId}]: No response content from Gemini for option matching.`);

            // Check if there are any safety issues or other reasons for no content
            if (data.candidates?.[0]?.finishReason) {
                console.warn(`AI-Service [${requestId}]: Gemini finish reason:`, data.candidates[0].finishReason);
            }

            throw new Error(`No response content from Gemini for option matching.`);
        }

        const aiResponse = data.candidates[0].content.parts[0].text.trim();
        return processAIOptionMatchResponse(aiResponse, options, requestId);
    } catch (error) {
        console.error(`AI-Service [${requestId}]: Error with Gemini option matching:`, error);

        // If it's an abort error (timeout), provide a specific message
        if (error.name === 'AbortError') {
            console.error(`AI-Service [${requestId}]: Gemini request timed out`);
        }

        return null;
    }
}

// Process AI response for option matching
function processAIOptionMatchResponse(aiResponse, options, requestId = 'unknown') {
    // Parse the response as JSON
    let matchResult;
    try {
        // Clean up the response if needed (remove markdown code blocks, etc.)
        const cleanedResponse = aiResponse.replace(/^```json\s*/, '').replace(/\s*```$/, '').trim();
        matchResult = JSON.parse(cleanedResponse);
        console.log(`AI-Service [${requestId}]: Parsed match result:`, matchResult);
    } catch (e) {
        console.error(`AI-Service [${requestId}]: Failed to parse JSON response:`, e);

        // Fallback: Try to parse as a simple number if JSON parsing fails
        const matchIndex = parseInt(aiResponse, 10);
        if (!isNaN(matchIndex) && matchIndex >= 0 && matchIndex <= options.length) {
            matchResult = { index: matchIndex, confidence: 100, matchType: "exact" };
            console.log(`AI-Service [${requestId}]: Fallback to simple number parsing:`, matchResult);
        } else {
            console.error(`AI-Service [${requestId}]: Invalid response format from AI:`, aiResponse);
            return null;
        }
    }

    // Validate the match result
    if (!matchResult || typeof matchResult.index !== 'number') {
        console.error(`AI-Service [${requestId}]: Invalid match result structure:`, matchResult);
        return null;
    }

    const matchIndex = matchResult.index;

    if (matchIndex < 0 || matchIndex > options.length) {
        console.error(`AI-Service [${requestId}]: Invalid option index from AI:`, matchIndex);
        return null;
    }

    if (matchIndex === 0) {
        console.log(`AI-Service [${requestId}]: No matching option found by AI.`);
        return null;
    }

    // Return the matched option (adjusting for 0-based array index)
    const matchedOption = {...options[matchIndex - 1]};  // Create a copy of the option to avoid modifying the original

    // Add match metadata to the option
    matchedOption.confidence = matchResult.confidence || 0;
    matchedOption.matchType = matchResult.matchType || "unknown";

    // Use the text and value from the matchResult if provided (to ensure consistency)
    if (matchResult.text) {
        matchedOption.text = matchResult.text;
    }
    if (matchResult.value) {
        matchedOption.value = matchResult.value;
    }

    console.log(`AI-Service [${requestId}]: AI found match (${matchedOption.matchType}, confidence: ${matchedOption.confidence}%):`, matchedOption);
    return matchedOption;
}

// Get the form analysis prompt
function getFormAnalysisPrompt(processedHTML, reportFieldNames = []) {
    // Create a string of available report field names if any are provided
    const reportFieldsSection = reportFieldNames.length > 0
        ? `\nAvailable Report Field Names (use these for the "match" property when appropriate):\n${reportFieldNames.map(name => `- ${name}`).join('\n')}\n`
        : '';

    return `Analyze the provided HTML snippet, focusing *only* on interactive form elements (inputs, textareas, selects, radio/checkbox groups, custom controls acting like inputs) that are likely visible to the user. Ignore hidden fields (\`type="hidden"\`, \`style*="display: none"\`, \`hidden\` attribute). Generate a JSON array where each object represents a distinct form field or group. Adhere strictly to these rules:
1.  **Output Format:** Valid JSON array \`[ {field1}, {field2}, ... ]\`. NO markdown fences (\`\`\`json\`).
2.  **Selectors:** Use simplest CSS: \`[id='value']\` or \`[name='value']\`. ID MUST be preferred if available and unique-like. Use single quotes for values. NO parent selectors, NO '#id'. If no suitable id/name, use \`""\`. Selector MUST target the *visible interactive element* (could be a \`div\` for custom selects).
3.  **Empty Values:** Use empty string \`""\`, NOT \`null\`.
4.  **Labels:** Find associated label (\`<label for>\`, \`aria-labelledby\`, \`aria-label\`, nearby heading). If none, use \`""\`.
5.  **Required:** Check for \`required\` or \`aria-required="true"\`. Default \`false\`.
6.  **MaxLength:** Only for text inputs/textareas. Get integer value from \`maxlength\` attribute. If none/not applicable, use \`""\`.
7.  **Element Nature:** \`"native"\` if interactive element is \`<input>\`, \`<select>\`, \`<textarea>\`. \`"custom"\` otherwise (e.g., styled \`div\`).
8.  **Types:** Use specific types like \`input-text\`, \`input-email\`, \`textarea\`, \`select\`, \`radio-group\`, \`checkbox-group\`, \`button\`, \`custom-input-text\`, \`custom-input-email\`, \`custom-textarea\`, \`custom-select\`, \`custom-radio-group\`, \`custom-checkbox-group\`. Use \`button\` type for \`<button>\` elements, submit inputs, and clickable elements that perform actions.
9.  **Groups (Radio/Checkbox):** Create a single entry with \`type: "radio-group"\` or \`"checkbox-group"\`. Include an \`"options"\` array. Group label is the main question/legend. Option selector targets the individual input/control. Group selector is \`""\`.
10. **Match:** Include a \`"match"\` property that suggests a potential report field match from the provided list of report field names. Match based on the field's label, purpose, or semantic similarity. Use EXACT spelling from the provided list. Leave as empty string if no good match can be determined.

**Field Object Structure (Standard):**
\`\`\`json
{
  "type": "specific-type",
  "label": "Visible Label Text",
  "match": "",
  "selector": "[id='unique_id']",
  "required": false,
  "maxlength": number or "",
  "value": "",
  "element_nature": "native"
}
\`\`\`
**Field Object Structure (Group):**
\`\`\`json
{
  "type": "radio-group",
  "label": "Group Question?",
  "match": "",
  "selector": "",
  "required": true or false,
  "maxlength": "",
  "value": "",
  "defaultOption": "",
  "element_nature": "custom",
  "options": [ { "label": "Option 1", "selector": "[id='option1_id']" }, ... ]
}
\`\`\`

${reportFieldsSection}
HTML to analyze:
\`\`\`html
${processedHTML}
\`\`\`
`;
}

// Get the CSS selector for a custom-select element option
function getCustomSelectOptionSelector(html, targetValue, fieldLabel = '') {
    return `I need to find the best matching option in a custom dropdown menu and provide a simple, direct CSS selector to click it.

Field Label: "${fieldLabel || 'Unknown'}"
Target Value: "${targetValue}"

Below is the HTML of the dropdown options:
\`\`\`html
${html}
\`\`\`

Your task:
1. Analyze the HTML structure of this custom dropdown
2. Find the option that best matches the target value "${targetValue}"
3. Create a SIMPLE, DIRECT CSS selector that will precisely target this option element for clicking
4. Focus on finding elements that are likely to be clickable options in the dropdown
5. Follow this STRICT PRIORITY ORDER for creating selectors:
   a. FIRST CHOICE: Use ID if available: [id='example-id'] (always use attribute selector format with single quotes for IDs)
   b. SECOND CHOICE: Use a unique class: .unique-class-name
   c. THIRD CHOICE: Use a data attribute: [data-value='xyz']
   d. LAST RESORT: If none of the above are available, use a simple path like ".parent-class .child-class"

EXTREMELY IMPORTANT - SELECTOR RESTRICTIONS:
- NEVER use :contains() in your selector - it will break the code
- NEVER use :has(), :nth-child(), :first-child, or any other pseudo-selectors
- NEVER use selectors that depend on text content like span:contains('Photos & Images')
- BAD EXAMPLES (DO NOT USE):
  * span:contains('Photos & Images')
  * div:has(.option-class)
  * li:nth-child(3)
  * .items:first-child
- GOOD EXAMPLES (USE THESE PATTERNS):
  * [id='specific-id']
  * .unique-class-name
  * [data-value='xyz']
  * .parent-class .child-class

Consider semantic similarity, not just exact matches when finding the option that best matches the target value.
Use the field label "${fieldLabel || 'Unknown'}" for context to improve matching accuracy.

Your response MUST be formatted exactly like this, with NO COMMENTS:

\`\`\`
{
  "selector": "[id='option-123']",
  "confidence": 85,
  "matchedText": "Option Text"
}
\`\`\`

CRITICAL REQUIREMENTS:
- Use the SIMPLEST possible selector that will uniquely identify the element
- Prefer ID > class > data attribute > simple path (in that order)
- NEVER use :contains() or any other pseudo-selectors - they will break the code
- Return ONLY the JSON object with NO COMMENTS
- Do not include any explanations or additional text
- DO NOT include comments in the JSON (no // or /* */ anywhere)
- DO NOT use placeholders like "string" or "number" - use actual values
- Make sure your response is valid JSON that can be parsed directly
- The response must be parseable by JSON.parse() after removing the code block markers
- Do not include the word "json" after the opening backticks
- Do not include any trailing commas in the JSON object`;
}

// Get the option matching prompt for standard select elements
function getOptionMatchingPrompt(options, targetValue, fieldLabel = '') {
    return `I need to find the best matching option from a dropdown menu for a given target value.

${fieldLabel ? `Field Label: "${fieldLabel}"` : ''}
Target Value: "${targetValue}"

Available Options:
${options.map((opt, idx) => `${idx + 1}. Value: "${opt.value}", Text: "${opt.text}"`).join('\n')}

Please analyze these options using the following approach:
1. Look for the most semantically similar option to the target value by considering exact matches, variations in wording, and synonyms.
2. Include partial matches, abbreviations, or shorthand forms that closely align with the target value.
3. If no direct semantic match exists, identify any categories or parent group names in the dropdown options that would logically contain the target value.
4. If the target value is a specific term, select a broader category that may encapsulate the term.
5. For a refined match, consider hierarchical relationships or groupings indicated by indentation or structure in the option list.
${fieldLabel ? `6. Consider the field label "${fieldLabel}" to provide context for the selection and improve matching accuracy.` : ''}

Your response should be in the following format:
{
  "index": number,  // 1-based index of the best match (0 if no match found)
  "value": string,  // The actual value of the selected option (empty if no match)
  "text": string,   // The display text of the selected option (empty if no match)
  "confidence": number,  // 0-100 confidence score based on how similar the option is to the target
  "matchType": "exact|similar|category|none"  // Type of match: exact match, similar match, category match, or no match
}

Return ONLY valid JSON with these fields, no explanation or additional text. Ensure accuracy in the index and match type based on the best possible match.`;
}

// Find best matching option in a custom-select using AI
async function findCustomSelectOption(html, targetValue, fieldLabel = '') {
    // Add a unique request ID to track this specific request through the logs
    const requestId = Math.random().toString(36).substring(2, 10);

    if (!html || !targetValue) {
        console.error(`AI-Service [${requestId}]: Invalid HTML or target value for custom-select matching.`);
        return null;
    }

    try {
        console.log(`AI-Service [${requestId}]: Finding best matching option in custom-select for value:`, targetValue);
        if (fieldLabel) {
            console.log(`AI-Service [${requestId}]: Using field label for context:`, fieldLabel);
        }

        // Log the HTML being sent to the AI (truncated for readability)
        const htmlPreview = html.length > 200 ? html.substring(0, 200) + '...' : html;
        console.log(`AI-Service [${requestId}]: HTML preview being analyzed:`, htmlPreview);
        console.log(`AI-Service [${requestId}]: Full HTML length: ${html.length} characters`);

        // Use the appropriate AI model based on user preference
        const aiModel = await getCurrentAIModel();
        console.log(`AI-Service [${requestId}]: Using ${aiModel} model for custom-select option matching`);

        // Create the prompt
        const prompt = getCustomSelectOptionSelector(html, targetValue, fieldLabel);
        console.log(`AI-Service [${requestId}]: Prompt created for AI (length: ${prompt.length} characters)`);


        let response = null;
        if (aiModel === 'gemini') {
            response = await fetch(`${GEMINI_API_URL}?key=${GEMINI_API_KEY}`, {
                method: "POST",
                headers: {
                    "Content-Type": "application/json"
                },
                body: JSON.stringify({
                    contents: [
                        {
                            parts: [
                                {
                                    text: "You are a specialized AI assistant that helps find elements in HTML and create precise CSS selectors."
                                },
                                {
                                    text: prompt
                                }
                            ]
                        }
                    ],
                    generationConfig: {
                        temperature: 0.1,
                        maxOutputTokens: 150,
                        topP: 0.95,
                        topK: 40
                    }
                })
            });

            if (!response.ok) {
                const errorBody = await response.text();
                console.error("Gemini API Error Body:", errorBody);
                throw new Error(`Gemini API Error: ${response.status} ${response.statusText}`);
            }

            const data = await response.json();
            if (!data.candidates?.[0]?.content?.parts?.[0]?.text) {
                throw new Error(`AI-Service [${requestId}]: No response content from Gemini for custom-select matching.`);
            }

            const aiResponse = data.candidates[0].content.parts[0].text.trim();
            return processCustomSelectResponse(aiResponse, requestId);
        } else {
            response = await fetch(OPENAI_API_URL, {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                    "Authorization": `Bearer ${OPENAI_API_KEY}`
                },
                body: JSON.stringify({
                    model: OPENAI_MODEL,
                    messages: [
                        { role: "system", content: "You are a specialized AI assistant that helps find elements in HTML and create precise CSS selectors." },
                        { role: "user", content: prompt }
                    ],
                    temperature: 0.1,
                    max_tokens: 150,
                    response_format: { type: "json_object" }
                })
            });

            if (!response.ok) {
                const errorBody = await response.text();
                console.error("OpenAI API Error Body:", errorBody);
                throw new Error(`OpenAI API Error: ${response.status} ${response.statusText}`);
            }

            const data = await response.json();
            if (!data.choices?.[0]?.message?.content) {
                throw new Error(`AI-Service [${requestId}]: No response content from OpenAI for custom-select matching.`);
            }

            const aiResponse = data.choices[0].message.content.trim();
            return processCustomSelectResponse(aiResponse, requestId);
        }
    } catch (error) {
        console.error(`AI-Service [${requestId}]: Error finding custom-select option:`, error);
        return null;
    }
}

// Process AI response for custom-select option matching
function processCustomSelectResponse(aiResponse, requestId = 'unknown') {
    // Log the raw response
    console.log(`AI-Service [${requestId}]: Raw AI response (first 300 chars):`,
        aiResponse.length > 300 ? aiResponse.substring(0, 300) + '...' : aiResponse);
    console.log(`AI-Service [${requestId}]: Raw response length: ${aiResponse.length} characters`);

    // Parse the response as JSON
    let result;
    try {
        // Try to extract JSON if the response contains HTML or other non-JSON content
        let cleanedResponse = aiResponse;

        // First, try to extract JSON from code blocks
        const codeBlockMatch = aiResponse.match(/```(?:json)?\s*([\s\S]*?)\s*```/);
        if (codeBlockMatch && codeBlockMatch[1]) {
            cleanedResponse = codeBlockMatch[1].trim();
            console.log(`AI-Service [${requestId}]: Extracted JSON from code block`);
        }
        // If no code block or empty code block, check if the response contains HTML tags
        else if (aiResponse.includes('<') && aiResponse.includes('>')) {
            console.warn(`AI-Service [${requestId}]: Response contains HTML tags, attempting to extract JSON`);

            // Try to find JSON-like content within the response
            const jsonMatch = aiResponse.match(/\{[\s\S]*"selector"[\s\S]*\}/);
            if (jsonMatch) {
                cleanedResponse = jsonMatch[0];
                console.log(`AI-Service [${requestId}]: Extracted potential JSON from HTML response`);
            } else {
                throw new Error("Could not extract JSON from HTML response");
            }
        } else {
            // Standard cleanup (remove markdown code blocks, etc.)
            cleanedResponse = aiResponse.replace(/^```(?:json)?\s*/, '').replace(/\s*```$/, '').trim();
        }

        // Remove any comments from the JSON
        cleanedResponse = cleanedResponse.replace(/\/\/.*$/gm, '');

        console.log(`AI-Service [${requestId}]: Cleaned response (first 300 chars):`,
            cleanedResponse.length > 300 ? cleanedResponse.substring(0, 300) + '...' : cleanedResponse);

        // Try to parse the JSON
        try {
            result = JSON.parse(cleanedResponse);
        } catch (parseError) {
            // If parsing fails, try to fix common JSON issues
            console.warn(`AI-Service [${requestId}]: Initial JSON parsing failed, attempting to fix JSON: ${parseError.message}`);

            // Replace single quotes with double quotes
            let fixedJson = cleanedResponse.replace(/'/g, '"');

            // Ensure property names have double quotes
            fixedJson = fixedJson.replace(/(\w+):/g, '"$1":');

            // Remove trailing commas in objects and arrays
            fixedJson = fixedJson.replace(/,\s*([}\]])/g, '$1');

            // Fix missing quotes around string values
            fixedJson = fixedJson.replace(/"([^"]+)":\s*([^",\{\[\]\}][^,\{\[\]\}]*[^",\{\[\]\}])([,\}\]])/g, '"$1": "$2"$3');

            console.log(`AI-Service [${requestId}]: Fixed JSON attempt:`, fixedJson);

            try {
                // Try parsing again
                result = JSON.parse(fixedJson);
                console.log(`AI-Service [${requestId}]: Successfully parsed JSON after fixing format issues`);
            } catch (secondError) {
                console.error(`AI-Service [${requestId}]: Second parsing attempt failed: ${secondError.message}`);

                // Last resort: try to manually extract the selector, confidence, and matchedText
                console.log(`AI-Service [${requestId}]: Attempting manual extraction of JSON properties`);

                const selectorMatch = cleanedResponse.match(/"selector"\s*:\s*"([^"]+)"/);
                const confidenceMatch = cleanedResponse.match(/"confidence"\s*:\s*(\d+)/);
                const matchedTextMatch = cleanedResponse.match(/"matchedText"\s*:\s*"([^"]+)"/);

                if (selectorMatch) {
                    result = {
                        selector: selectorMatch[1],
                        confidence: confidenceMatch ? parseInt(confidenceMatch[1]) : 0,
                        matchedText: matchedTextMatch ? matchedTextMatch[1] : ''
                    };
                    console.log(`AI-Service [${requestId}]: Manually extracted JSON properties:`, result);
                } else {
                    throw new Error("Could not manually extract selector from response");
                }
            }
        }

        console.log(`AI-Service [${requestId}]: Parsed custom-select result:`, result);
    } catch (e) {
        console.error(`AI-Service [${requestId}]: Failed to parse JSON response:`, e.message);

        // Log the problematic part of the response where the error occurred
        if (e.message.includes('at position')) {
            try {
                const position = parseInt(e.message.match(/position (\d+)/)[1]);
                const errorContext = aiResponse.substring(
                    Math.max(0, position - 20),
                    Math.min(aiResponse.length, position + 20)
                );
                console.error(`AI-Service [${requestId}]: Error context around position ${position}:`, errorContext);
            } catch (contextError) {
                console.error(`AI-Service [${requestId}]: Could not extract error position context:`, contextError);
            }
        }

        // Log the full response for debugging
        console.error(`AI-Service [${requestId}]: Full problematic response:`, aiResponse);
        return null;
    }

    // Validate the result
    if (!result || !result.selector) {
        console.error(`AI-Service [${requestId}]: Invalid custom-select result structure:`, result);
        return null;
    }

    return {
        selector: result.selector,
        confidence: result.confidence || 0,
        matchedText: result.matchedText || ''
    };
}

console.log("sidepanel-ai-service.js loaded");
