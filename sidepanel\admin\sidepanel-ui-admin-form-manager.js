// File: admin/sidepanel/sidepanel-ui-admin-form-manager.js

const adminFieldsContentUI = document.getElementById('admin-form-fields-content'); // Define it here

function updateAdminFormFields(formData, actionData = []) {
    if (!adminFieldsContentUI) {
        console.error("Admin form fields container not found!");
        return;
    }
    adminFieldsContentUI.innerHTML = '';
    if ((!formData || formData.length === 0) && (!actionData || actionData.length === 0)) {
        adminFieldsContentUI.innerHTML = '<p class="loading-placeholder">No configuration found. Use "Add Page" to create a new page, then "Select Elements" to add fields.</p>'; // Use class
        return;
    }

    // Group fields by page
    const fieldsByPage = {};

    // Check if fields have page information
    const hasPageInfo = formData.some(field => field.pageId);

    if (hasPageInfo) {
        // Group fields by page
        formData.forEach((field) => {
            const pageId = field.pageId || 'default';
            if (!fieldsByPage[pageId]) {
                fieldsByPage[pageId] = [];
            }
            fieldsByPage[pageId].push(field);
        });

        // Create page accordions for each page
        Object.keys(fieldsByPage).forEach((pageId, pageIndex) => {
            const pageFields = fieldsByPage[pageId];
            const pageTitle = pageId === 'default' ? 'Page 1' : `Page ${pageIndex + 1}`;

            // Create page accordion
            const pageElement = createPageAccordion(pageIndex, pageTitle);
            adminFieldsContentUI.appendChild(pageElement);

            // Get the fields container for this page
            const fieldsContainer = pageElement.querySelector('.page-fields-container');

            // Add fields to the page
            pageFields.forEach((field, fieldIndex) => {
                const fieldElement = createAdminFormFieldElement(fieldIndex, field);
                fieldElement.setAttribute('data-page-id', pageId);
                fieldsContainer.appendChild(fieldElement);
            });

            // Add actions to the page
            const pageActions = actionData.filter(action => action.pageId === pageId);
            pageActions.forEach((action, actionIndex) => {
                const actionElement = createAdminActionElement(actionIndex, action);
                actionElement.setAttribute('data-page-id', pageId);
                fieldsContainer.appendChild(actionElement);
            });

            // Initialize accordion for this page's fields and actions
            initializeAdminAccordionAndSortable(fieldsContainer);
        });

        // Initialize page accordions
        initializePageAccordions(adminFieldsContentUI);
    } else {
        // Create a default page accordion
        const pageElement = createPageAccordion(0, 'Page 1');
        adminFieldsContentUI.appendChild(pageElement);

        // Get the fields container for this page
        const fieldsContainer = pageElement.querySelector('.page-fields-container');

        // Add all fields to the default page
        if (formData && formData.length > 0) {
            formData.forEach((field, index) => {
                const fieldElement = createAdminFormFieldElement(index, field);
                fieldElement.setAttribute('data-page-id', 'page-accordion-0');
                fieldsContainer.appendChild(fieldElement);
            });
        }

        // Add all actions to the default page
        if (actionData && actionData.length > 0) {
            actionData.forEach((action, index) => {
                const actionElement = createAdminActionElement(index, action);
                actionElement.setAttribute('data-page-id', 'page-accordion-0');
                fieldsContainer.appendChild(actionElement);
            });
        }

        // Initialize accordion for the default page's fields and actions
        initializeAdminAccordionAndSortable(fieldsContainer);

        // Initialize page accordions
        initializePageAccordions(adminFieldsContentUI);
    }


}

function addGeneratedFieldsToAdminUI(generatedFields) {
    if (!adminFieldsContentUI) {
        console.error("Admin form fields container not found!");
        return;
    }
    if (!generatedFields || generatedFields.length === 0) return;

    // Remove placeholder if it exists
    const placeholder = adminFieldsContentUI.querySelector('p.loading-placeholder');
    if (placeholder) placeholder.remove();

    console.log("UI-Admin: Adding generated fields:", generatedFields);

    // Check if we have any page accordions
    const pageAccordions = adminFieldsContentUI.querySelectorAll('.page-accordion');

    if (pageAccordions.length === 0) {
        // Create a default page accordion if none exists
        const pageElement = createPageAccordion(0, 'Default Page');
        adminFieldsContentUI.appendChild(pageElement);

        // Initialize the page accordion
        initializePageAccordions(adminFieldsContentUI);
    }

    // Get the current page ID from the window object (set when Select Elements is clicked)
    // or use the first page if not set
    const targetPageId = window.currentPageId || pageAccordions[0]?.getAttribute('data-page-id') || 'page-accordion-0';

    // Find the target page's fields container
    const fieldsContainer = document.getElementById(`fields-container-${targetPageId}`);

    if (!fieldsContainer) {
        console.error(`Fields container not found for page: ${targetPageId}`);
        return;
    }

    // Get the next field index for this page
    const startIndex = Array.from(fieldsContainer.children).filter(el => el.matches('.dynamic-field-container')).length;

    // Add fields to the page
    generatedFields.forEach((fieldData, index) => {
        // Add page ID to the field data
        fieldData.pageId = targetPageId;

        // Create the field element
        const fieldElement = createAdminFormFieldElement(startIndex + index, fieldData);

        // Add the page ID to the field element
        fieldElement.setAttribute('data-page-id', targetPageId);

        // Add the field to the page
        fieldsContainer.appendChild(fieldElement);
    });

    // Initialize accordion for this page's fields
    initializeAdminAccordionAndSortable(fieldsContainer);

    // Reset the current page ID
    window.currentPageId = null;


}

console.log("sidepanel-ui-admin-form-manager.js loaded");