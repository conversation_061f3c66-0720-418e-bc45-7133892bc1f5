// sidepanel-ui-common.js
// Handles common DOM elements, shared UI updates, and utility UI functions.

// --- Common DOM Element References ---
const statusSiteUrlElementUI = document.getElementById('status-site-url');
const statusReportIdElementUI = document.getElementById('status-report-id');
const tabsContainerUI = $('#tabs'); // jQuery object for tabs
// Note: selectButton was removed from the UI, so no longer needed
// Add references to the selector generation buttons if needed globally,
// otherwise they can be handled within their respective main modules.

// --- Common UI Update Functions ---

function updateUnifiedStatus() {
    // Access state directly from sidepanel-logic-common.js
    statusSiteUrlElementUI.textContent = `Site URL: ${state.currentSiteUrl || 'N/A'}`;
    statusReportIdElementUI.textContent = `Report ID: ${state.activeReport?.report_id || 'N/A'}`;
}

function clearSidePanelContent() {
    // Clear admin panel (call admin UI function)
    updateAdminFormFields([]); // (ui-admin)

    // Clear submit panel (call submit UI function)
    updateSubmitFormFields([]); // (ui-submit)

    // Clear tools panel (call tools UI function)
    if (typeof clearToolsTabContent === 'function') {
        clearToolsTabContent(); // (ui-tools)
    }

    // Clear report site fields (call submit UI function)
    clearReportSiteFields(); // (ui-submit)

    // Update status
    updateUnifiedStatus();
}

function switchToAdminTab() {
    tabsContainerUI.tabs("option", "active", 0); // 0 is the index of the Admin tab
}

function scrollAdminFieldsToBottom() {
     // adminFieldsContentUI is defined in ui-admin.js
     const lastField = adminFieldsContentUI?.lastElementChild;
     lastField?.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
}


// Reset state of all buttons that initiate selection mode
function resetSelectButtons() {
    // Clear global selection mode flag
    window.isSelectionModeActiveInSidepanel = false;

    // Reset all selector generation buttons
    document.querySelectorAll('.generate-selector-path-button').forEach(button => {
        button.disabled = false;
        button.innerHTML = '<svg><use href="#icon-generate"></use></svg>'; // Restore icon
    });

    // Reset all "Select Elements" buttons
    document.querySelectorAll('.select-elements-button').forEach(button => {
        button.disabled = false;
        button.textContent = 'Select Elements';
    });
}

// Set up tab activation event handling
$(document).ready(function() {
    // Listen for jQuery UI tab activation
    $(tabsContainerUI).on('tabsactivate', function(event, ui) {
        const tabId = ui.newPanel.attr('id');

        // Dispatch a custom event for the activated tab
        document.dispatchEvent(new CustomEvent('tabActivated', {
            detail: { tabId: tabId }
        }));

        console.log(`UI-Common: Tab activated: ${tabId}`);
    });
});

