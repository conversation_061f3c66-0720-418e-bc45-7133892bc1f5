// File: admin/sidepanel/sidepanel-ui-admin-renderer.js
function updateDefaultOptionDropdown(fieldId) {
    // Skip if fieldId is not provided or invalid
    if (!fieldId) {
        return;
    }

    // Try to find the field container using both data-field-id and data-field-index
    let fieldContainer = document.querySelector(`.dynamic-field-container[data-field-id="${fieldId}"]`);

    // If not found by data-field-id, try by data-field-index
    if (!fieldContainer) {
        fieldContainer = document.querySelector(`.dynamic-field-container[data-field-index="${fieldId.replace('dynamic-field-', '')}"]`);
    }

    // If still not found, silently return
    if (!fieldContainer) {
        return;
    }

    // Use class selectors instead of ID selectors
    const defaultOptionSelect = fieldContainer.querySelector('.field-default-option');
    const optionsContainer = fieldContainer.querySelector('.options-container');

    if (!defaultOptionSelect || !optionsContainer) {
        console.warn(`Could not find default option select or options container for field ${fieldId}`);
        return;
    }

    // Save the current selection
    const currentValue = defaultOptionSelect.value;

    // Clear existing options except the first "No Default" option
    while (defaultOptionSelect.options.length > 1) {
        defaultOptionSelect.remove(1);
    }

    // Get all option elements
    const optionItems = optionsContainer.querySelectorAll('.option-item');

    // Add each option to the dropdown
    optionItems.forEach((optItem, idx) => {
        // Use class selectors instead of ID selectors
        const labelElement = optItem.querySelector('.option-label');
        const selectorElement = optItem.querySelector('.option-selector');

        // Get the current label value from the input element
        const optLabel = labelElement?.value || `Option ${idx + 1}`;
        const optSelector = selectorElement?.value || '';

        // Also get the header text to ensure consistency
        const headerText = optItem.querySelector('h4 > span')?.textContent || optLabel;

        // Always add the option to the dropdown, even if selector is empty
        const option = document.createElement('option');
        option.value = optSelector; // This can be empty
        option.textContent = escapeHtml(optLabel); // Use escapeHtml from the input value
        option.selected = (optSelector === currentValue);
        defaultOptionSelect.appendChild(option);

        // Ensure the header text matches the label
        const headerElement = optItem.querySelector('h4 > span');
        if (headerElement && headerElement.textContent !== optLabel) {
            headerElement.textContent = optLabel;
        }
    });

    // If the current value is no longer in the dropdown, select the first option
     let valueExists = false;
     for (let i = 0; i < defaultOptionSelect.options.length; i++) {
         if (defaultOptionSelect.options[i].value === currentValue) {
             valueExists = true;
             break;
         }
     }

     if (!valueExists && currentValue) {
         defaultOptionSelect.selectedIndex = 0;
     } else if (currentValue) {
         // Ensure the correct option is selected
         defaultOptionSelect.value = currentValue;
     }

    // Dispatch a change event to notify any listeners
    defaultOptionSelect.dispatchEvent(new Event('change', { bubbles: true }));
}
// --- End of Helper function ---


function createAdminOptionElement(fieldId, optionIndex, optionData = {}) {
    // Assumes escapeHtml is available
    const optionId = `${fieldId}-option-${optionIndex}`;

    const optionElement = document.createElement('div');
    optionElement.className = 'option-item';
    optionElement.setAttribute('data-option-index', optionIndex);
    optionElement.setAttribute('data-field-id', fieldId);

    const headerElement = document.createElement('h4');
    headerElement.className = 'option-header ui-accordion-header ui-corner-top';
    headerElement.style.cursor = 'move';
    const headerText = document.createElement('span');
    headerText.textContent = optionData.label ? escapeHtml(optionData.label) : `Option ${optionIndex + 1}`;
    headerElement.appendChild(headerText);
    const removeButton = document.createElement('button');
    removeButton.innerHTML = '<svg><use href="#icon-delete"></use></svg>';
    removeButton.className = 'remove-option-button small-button danger';
    removeButton.title = "Remove Option";
    removeButton.setAttribute('data-field-id', fieldId);
    removeButton.setAttribute('data-option-index', optionIndex);
    headerElement.appendChild(removeButton);
    optionElement.appendChild(headerElement);

    const panelElement = document.createElement('div');
    panelElement.className = 'option-panel ui-accordion-content ui-corner-bottom';

    panelElement.innerHTML = `
        <div class="field-group">
            <label for="option-label-${optionId}">Option Label:</label>
            <input type="text" id="option-label-${optionId}" class="option-label" value="${escapeHtml(optionData.label || '')}">
        </div>
        <div class="field-group field-group-with-button">
            <label for="option-selector-${optionId}">Option Selector:</label>
            <input type="text" id="option-selector-${optionId}" class="option-selector" value="${escapeHtml(optionData.selector || '')}" placeholder="e.g., [id='opt1']">
            <button type="button" class="field-button generate-selector-path-button" data-target-selector="#option-selector-${optionId}" title="Generate Selector Path via Element Selection">
                <svg><use href="#icon-generate"></use></svg>
            </button>
        </div>
    `;
    optionElement.appendChild(panelElement);

    // Add event listeners for both label and selector inputs to update dropdown
    const labelInput = panelElement.querySelector(`#option-label-${optionId}`);
    labelInput?.addEventListener('input', (e) => {
        // Update the option header
        const newLabel = e.target.value ? escapeHtml(e.target.value) : `Option ${optionIndex + 1}`;
        headerText.textContent = newLabel;

        // Force immediate update of the dropdown
        updateDefaultOptionDropdown(fieldId);

        // Force another update after a short delay to ensure it's processed
        setTimeout(() => {
            updateDefaultOptionDropdown(fieldId);
        }, 100);
    });

    const selectorInput = panelElement.querySelector(`#option-selector-${optionId}`);
    if (selectorInput) {
        // Add comprehensive event listeners to ensure it catches all updates
        const handleSelectorUpdate = () => {
            // Update the default option dropdown
            updateDefaultOptionDropdown(fieldId);
            // Handle the selector without validation (highlighting happens on hover)
            debouncedHighlightSelector(selectorInput, false);
        };

        selectorInput.addEventListener('input', handleSelectorUpdate);
        selectorInput.addEventListener('change', handleSelectorUpdate);
        selectorInput.addEventListener('blur', handleSelectorUpdate);
        selectorInput.addEventListener('paste', () => {
             // Wait for the paste to complete
             setTimeout(() => handleSelectorUpdate(), 0);
        });
        selectorInput.addEventListener('keyup', handleSelectorUpdate); // Added keyup

        // Initial validation
        setTimeout(() => {
            // Process the selector without validation
            // Pass false to prevent highlighting on initial processing
            debouncedHighlightSelector(selectorInput, false);
        }, 500);

        // Force an update of the default option dropdown after a short delay
        setTimeout(() => {
            updateDefaultOptionDropdown(fieldId);
        }, 100);
    }

    return optionElement;
}


function createAdminFormFieldElement(fieldIndex, fieldData = {}) {
    // Assumes escapeHtml is available
    // Assumes state is available
    // Assumes createAdminOptionElement is available (defined in this same file)
    const fieldId = `dynamic-field-${fieldIndex}`;
    const fieldElement = document.createElement('div');
    fieldElement.className = 'dynamic-field-container';
    fieldElement.setAttribute('data-field-index', fieldIndex);
    fieldElement.setAttribute('data-field-id', fieldId); // Add data-field-id here for easier lookup

    const headerElement = document.createElement('h3');
    headerElement.className = 'field-header ui-accordion-header ui-corner-top';
    headerElement.style.position = 'relative';
    headerElement.style.cursor = 'move';
    const headerText = document.createElement('span');
    headerText.textContent = fieldData.label ? escapeHtml(fieldData.label) : `Field ${fieldIndex + 1}`;
    headerElement.appendChild(headerText);
    const removeButton = document.createElement('button');
    removeButton.innerHTML = '<svg><use href="#icon-delete"></use></svg>';
    removeButton.className = 'remove-field-button small-button danger';
    removeButton.title = "Remove Field";
    removeButton.setAttribute('data-field-index', fieldIndex);
    headerElement.appendChild(removeButton);
    fieldElement.appendChild(headerElement);

    const panelElement = document.createElement('div');
    panelElement.className = 'field-panel ui-accordion-content ui-corner-bottom';

    let optionsHtmlContent = '';
    if (fieldData.options?.length) {
        fieldData.options.forEach((opt, index) => {
            optionsHtmlContent += createAdminOptionElement(fieldId, index, opt).outerHTML;
        });
    }

    const fieldTypeOptions = ['input-text', 'input-url', 'input-email', 'input-file', 'input-number', 'input-date', 'textarea', 'select', 'radio-group', 'checkbox-group', 'button', 'custom-input-text', 'custom-input-url', 'custom-input-email', 'custom-input-file', 'custom-input-number', 'custom-input-date', 'custom-textarea', 'custom-select', 'custom-radio-group', 'custom-checkbox-group', 'dragdrop-input-file'];
    const fieldTypeSelectOptions = fieldTypeOptions.map(o => `<option value="${o}" ${fieldData.type === o ? 'selected' : ''}>${o}</option>`).join('');
    // Assumes state.reportFieldValues is available
    const reportFieldNames = Object.keys(state.reportFieldValues || {}).sort();
    const matchFieldOptions = reportFieldNames.map(f => `<option value="${f}" ${fieldData.match === f ? 'selected' : ''}>${escapeHtml(f)}</option>`).join('');
    const matchFieldSelectOptions = `<option value="" ${!fieldData.match ? 'selected' : ''}>-- Select Report Field --</option>${matchFieldOptions}`;

    panelElement.innerHTML = `
        <div class="field-group">
            <label for="field-label-${fieldId}">Label:</label>
            <input type="text" id="field-label-${fieldId}" class="field-label" value="${escapeHtml(fieldData.label || '')}">
        </div>
        <div class="field-group">
            <label for="field-type-${fieldId}">Field Type:</label>
            <select id="field-type-${fieldId}" class="field-type">${fieldTypeSelectOptions}</select>
        </div>
        <div class="field-group">
            <label for="field-match-${fieldId}">Match Report Field:</label>
            <select id="field-match-${fieldId}" class="field-match">${matchFieldSelectOptions}</select>
        </div>
        <div class="field-group field-group-with-button">
            <label for="field-selector-${fieldId}">Selector:</label>
            <input type="text" id="field-selector-${fieldId}" class="field-selector" value="${escapeHtml(fieldData.selector || '')}" placeholder="e.g., [id='user'] or [name='email']">
            <button type="button" class="field-button generate-selector-path-button" data-target-selector="#field-selector-${fieldId}" title="Generate Selector Path via Element Selection">
                <svg><use href="#icon-generate"></use></svg>
            </button>
        </div>
        <div class="field-group field-group-with-button option-selector-container" style="display: ${fieldData.type === 'custom-select' ? 'block' : 'none'}">
            <label for="field-option-selector-${fieldId}">Option Selector:</label>
            <input type="text" id="field-option-selector-${fieldId}" class="field-option-selector" value="${escapeHtml(fieldData.optionSelector || '')}" placeholder="e.g., .dropdown-menu .dropdown-item">
            <button type="button" class="field-button generate-selector-path-button" data-target-selector="#field-option-selector-${fieldId}" title="Generate Option Selector Path via Element Selection">
                <svg><use href="#icon-generate"></use></svg>
            </button>
        </div>
        <div class="field-group">
            <label for="field-required-${fieldId}">Required:</label>
            <select id="field-required-${fieldId}" class="field-required">
                <option value="false" ${!fieldData.required ? 'selected' : ''}>No</option>
                <option value="true" ${fieldData.required === true ? 'selected' : ''}>Yes</option>
            </select>
        </div>
        <div class="field-group field-maxlength-container" style="display: ${['input-text', 'input-url', 'input-email', 'textarea'].includes(fieldData.type) ? 'block' : 'none'}">
            <label for="field-maxlength-${fieldId}">Max Length:</label>
            <input type="number" id="field-maxlength-${fieldId}" class="field-maxlength" value="${escapeHtml(fieldData.maxlength || '')}">
        </div>
        <div class="field-group button-delay-container" style="display: ${fieldData.type === 'button' ? 'block' : 'none'}">
            <label for="field-delay-${fieldId}">Click Delay (seconds):</label>
            <select id="field-delay-${fieldId}" class="field-delay">
                <option value="" ${!fieldData.delay ? 'selected' : ''}>No Delay</option>
                <option value="1" ${fieldData.delay === '1' ? 'selected' : ''}>1 second</option>
                <option value="2" ${fieldData.delay === '2' ? 'selected' : ''}>2 seconds</option>
                <option value="3" ${fieldData.delay === '3' ? 'selected' : ''}>3 seconds</option>
                <option value="4" ${fieldData.delay === '4' ? 'selected' : ''}>4 seconds</option>
                <option value="5" ${fieldData.delay === '5' ? 'selected' : ''}>5 seconds</option>
            </select>
        </div>
        <div class="field-group">
            <label for="field-value-${fieldId}">Default Value:</label>
            <textarea id="field-value-${fieldId}" class="field-value" rows="2">${escapeHtml(fieldData.value || '')}</textarea>
        </div>
        <div class="options-section" style="display: ${['radio-group', 'checkbox-group', 'custom-radio-group', 'custom-checkbox-group'].includes(fieldData.type) ? 'block' : 'none'}">
            <h4>Options (for Radio, Checkbox)</h4>
            <div class="field-group">
                <label for="field-default-option-${fieldId}">Default Option:</label>
                <select id="field-default-option-${fieldId}" class="field-default-option">
                    <option value="">-- No Default Option --</option>
                    ${fieldData.options?.map((opt, idx) => {
                        // Always include the option, even if selector is empty
                        const optSelector = opt.selector || '';
                        const optLabel = opt.label || `Option ${idx + 1}`;
                        return `<option value="${escapeHtml(optSelector)}" ${fieldData.defaultOption === optSelector ? 'selected' : ''}>${escapeHtml(optLabel)}</option>`;
                    }).join('') || ''}
                </select>
            </div>
            <div class="options-container" id="options-container-${fieldId}">${optionsHtmlContent}</div>
            <button class="add-option-button small-button secondary" data-field-id="${fieldId}" style="margin-top: 10px;">+ Add Option</button>
        </div>
    `;
    fieldElement.appendChild(panelElement);

    const labelInput = panelElement.querySelector(`#field-label-${fieldId}`);
    labelInput?.addEventListener('input', (e) => {
        headerText.textContent = e.target.value ? escapeHtml(e.target.value) : `Field ${fieldIndex + 1}`;
    });

    // Add event listener for field type changes to show/hide the option selector and options section
    const fieldTypeSelect = panelElement.querySelector(`#field-type-${fieldId}`);
    const optionSelectorContainer = panelElement.querySelector('.option-selector-container');
    const optionsSection = panelElement.querySelector('.options-section');
    const buttonDelayContainer = panelElement.querySelector('.button-delay-container');
    const maxLengthContainer = panelElement.querySelector('.field-maxlength-container');

    fieldTypeSelect?.addEventListener('change', (e) => {
        // Show/hide the option selector container for custom-select
        if (optionSelectorContainer) {
            optionSelectorContainer.style.display = e.target.value === 'custom-select' ? 'block' : 'none';
        }

        // Show/hide the options section for radio-group, checkbox-group, custom-radio-group, custom-checkbox-group
        if (optionsSection) {
            const showOptionsTypes = ['radio-group', 'checkbox-group', 'custom-radio-group', 'custom-checkbox-group'];
            optionsSection.style.display = showOptionsTypes.includes(e.target.value) ? 'block' : 'none';
        }

        // Show/hide the button delay container for button type
        if (buttonDelayContainer) {
            buttonDelayContainer.style.display = e.target.value === 'button' ? 'block' : 'none';
        }

        // Show/hide the max length container for specific input types
        if (maxLengthContainer) {
            const showMaxLengthTypes = ['input-text', 'input-url', 'input-email', 'textarea'];
            maxLengthContainer.style.display = showMaxLengthTypes.includes(e.target.value) ? 'block' : 'none';
        }
    });

    // Add validation for field selector
    const fieldSelectorInput = panelElement.querySelector(`#field-selector-${fieldId}`);
    if (fieldSelectorInput) {
        // Add event listener for input/change/blur/paste events
        const handleFieldSelectorUpdate = () => {
            debouncedHighlightSelector(fieldSelectorInput, false); // Assumes debouncedHighlightSelector is available
            // Trigger update for the default option dropdown just in case (though it's option selectors that matter most)
             updateDefaultOptionDropdown(fieldId);
        };
        fieldSelectorInput.addEventListener('input', handleFieldSelectorUpdate);
        fieldSelectorInput.addEventListener('change', handleFieldSelectorUpdate);
        fieldSelectorInput.addEventListener('blur', handleFieldSelectorUpdate);
        fieldSelectorInput.addEventListener('paste', () => { setTimeout(() => handleFieldSelectorUpdate(), 0); });
        fieldSelectorInput.addEventListener('keyup', handleFieldSelectorUpdate); // Added keyup

        // Initial validation
        setTimeout(() => {
            debouncedHighlightSelector(fieldSelectorInput, false); // Assumes debouncedHighlightSelector is available
        }, 500);
    }

    // Initialize default option dropdown content after rendering options
    setTimeout(() => {
         updateDefaultOptionDropdown(fieldId);
    }, 100);

    // Force another update after a longer delay to ensure it's processed
    setTimeout(() => {
        updateDefaultOptionDropdown(fieldId);
    }, 500);

    return fieldElement;
}

console.log("sidepanel-ui-admin-renderer.js loaded");