console.log("sidepanel-main-submit.js loading");

const saveReportSiteButtonMain = document.getElementById('saveReportSiteButton');
const submitTabContent = document.getElementById('submit-tab');

function setupSubmitEventListeners() {
    console.log("Main-Submit: Setting up submit event listeners...");
    saveReportSiteButtonMain?.addEventListener('click', handleSaveReportSiteClick);

    // Add event listener for the new screenshot capture button
    const addScreenshotButton = document.getElementById('add-screenshot-button');
    addScreenshotButton?.addEventListener('click', handleAddScreenshotClick);

    submitTabContent?.addEventListener('click', (event) => {
        const target = event.target;
        const button = target.closest('button');

        if (!button) return;

        if (button.matches('.upload-url-button')) {
            handleImageUrlUploadButtonClick(button);
        } else if (button.matches('.match-page-button')) {
            const pageId = button.getAttribute('data-page-id');
            if (pageId) {
                handleMatchPage(pageId);
            }
        } else if (button.matches('.remove-screenshot')) {
            handleRemoveScreenshotClick(button);
        }
    });
    console.log("Main-Submit: Submit event listeners setup complete.");
}



// Function to match fields and execute actions in a specific page
async function matchFieldsInPage(pageFields, pageActions = []) {
    if (!state.currentTabId) {
        alert('Active tab not identified.');
        return;
    }
    if (!state.activeReport) {
        alert('No active report selected.');
        return;
    }
    if ((!pageFields || pageFields.length === 0) && (!pageActions || pageActions.length === 0)) {
        alert('No fields or actions found to process.');
        return;
    }

    // Log the fields and actions we're going to process
    console.log('Processing fields:', pageFields);
    console.log('Processing actions:', pageActions);

    let successCount = 0;
    let errorCount = 0;
    let itemsProcessed = 0;

    // Create a combined array of fields and actions in the order they appear in the UI
    const combinedItems = [];

    // Add fields with their type
    pageFields.forEach(field => {
        combinedItems.push({ type: 'field', data: field });
    });

    // Add actions with their type
    pageActions.forEach(action => {
        combinedItems.push({ type: 'action', data: action });
    });

    // Sort by UI order if we can determine it (for now, process fields first, then actions)
    // TODO: In the future, we could determine actual UI order by querying the DOM

    console.log(`Processing ${combinedItems.length} items (${pageFields.length} fields, ${pageActions.length} actions)`);

    // Process each item (field or action) in order
    for (const item of combinedItems) {
        // Add a small delay between processing each item (0.25 seconds)
        if (itemsProcessed > 0) {
            // Only add delay after the first item
            await new Promise(resolve => setTimeout(resolve, 250));
        }

        if (item.type === 'action') {
            // Process action
            await processAction(item.data);
            itemsProcessed++;
            successCount++; // For now, assume actions succeed
        } else {
            // Process field
            const field = item.data;

            // Get the field properties directly from the field object
            const selector = field.selector || '';
            const fieldType = field.type || '';
            const defaultOption = field.defaultOption || '';

        // Get the value from reportFieldValues using the match property
        const valueToSet = field.match && state.reportFieldValues ?
                          state.reportFieldValues[field.match] || '' :
                          field.value || '';

        // Handle different field types
        if (fieldType === 'button') {
            // For button type, simply click the selector
            itemsProcessed++;

            if (selector && selector.trim() !== '') {
                try {
                    console.log(`Main-Submit: Attempting to click button with selector "${selector}"${field.delay ? ` with ${field.delay}s delay` : ''}`);

                    const response = await chrome.runtime.sendMessage({
                        action: 'proxyRequestToContentScript',
                        targetTabId: state.currentTabId,
                        contentScriptAction: 'clickElement',
                        payload: {
                            selector: selector,
                            delay: field.delay
                        }
                    });

                    if (response?.success) {
                        console.log(`Main-Submit: Successfully clicked button with selector "${selector}"`);
                        successCount++;
                    } else {
                        throw new Error(response?.error || 'Content script failed to click button.');
                    }
                } catch (error) {
                    errorCount++;
                    console.error(`Main-Submit: Click Error for button with selector "${selector}":`, error);
                }
            } else {
                // If no selector, log it and count as success to avoid error
                console.log(`Main-Submit: No selector found for button field "${field.label || 'Unnamed'}"`);
                successCount++; // Count as success to avoid error
            }
            // Skip the rest of the loop for button type
            continue;
        }
        else if (fieldType === 'radio-group' || fieldType === 'checkbox-group' ||
            fieldType === 'custom-radio-group' || fieldType === 'custom-checkbox-group') {

            itemsProcessed++;

            // Determine the value to use following priority order:
            // 1. Match Report Field
            // 2. Default Value
            // 3. Default Option
            let valueToUse = '';
            let valueSource = '';

            // Priority 1: Match Report Field
            if (field.match && field.match.trim() !== '' && state.reportFieldValues && state.reportFieldValues[field.match]) {
                valueToUse = state.reportFieldValues[field.match];
                valueSource = 'Match Report Field';
            }
            // Priority 2: Default Value
            else if (field.value && field.value.trim() !== '') {
                valueToUse = field.value;
                valueSource = 'Default Value';
            }
            // Priority 3: Default Option (handled separately below)

            console.log(`Main-Submit: Processing ${fieldType} field "${field.label || 'Unnamed'}" - Value source: ${valueSource || 'Default Option'}, Value: "${valueToUse}"`);

            // If we have a value to match against options
            if (valueToUse && valueToUse.trim() !== '' && field.options && field.options.length > 0) {
                try {
                    console.log(`Main-Submit: Attempting to find best matching option for ${fieldType} field "${field.label || 'Unnamed'}" with value "${valueToUse}" from ${valueSource}`);

                    // Convert options to format expected by findBestMatchingOption
                    const optionsForMatching = field.options.map(opt => ({
                        value: opt.selector,
                        text: opt.label
                    }));

                    // Use AI to find the best matching option
                    const matchedOption = await findBestMatchingOption(optionsForMatching, valueToUse, field.label || '');

                    if (matchedOption) {
                        console.log(`Main-Submit: Found matching option for ${fieldType}: "${matchedOption.text}" with selector "${matchedOption.value}"`);

                        // Use setCheckboxRadioState for both radio and checkbox groups
                        const response = await chrome.runtime.sendMessage({
                            action: 'proxyRequestToContentScript',
                            targetTabId: state.currentTabId,
                            contentScriptAction: 'setCheckboxState', // This will be handled by handleSetCheckboxRadioState
                            payload: { selector: matchedOption.value }
                        });

                        if (response?.success) {
                            console.log(`Main-Submit: Successfully clicked ${fieldType} matched option with selector "${matchedOption.value}"`);
                            successCount++;
                        } else {
                            throw new Error(response?.error || 'Content script failed to click element.');
                        }
                    } else {
                        console.log(`Main-Submit: No matching option found for ${fieldType} field "${field.label || 'Unnamed'}" with value "${valueToUse}" from ${valueSource}`);

                        // Fall back to default option if available
                        if (defaultOption && defaultOption.trim() !== '') {
                            console.log(`Main-Submit: Falling back to default option with selector "${defaultOption}"`);
                            await clickDefaultOption();
                        } else {
                            console.log(`Main-Submit: No default option available as fallback - doing nothing`);
                            // Do nothing - don't count as success or error
                        }
                    }
                } catch (error) {
                    console.error(`Main-Submit: Error finding matching option for ${fieldType} field:`, error);

                    // Fall back to default option if available
                    if (defaultOption && defaultOption.trim() !== '') {
                        console.log(`Main-Submit: Error occurred, falling back to default option with selector "${defaultOption}"`);
                        await clickDefaultOption();
                    } else {
                        console.log(`Main-Submit: Error occurred and no default option available - doing nothing`);
                        // Do nothing - don't count as success or error
                    }
                }
            }
            // If no value to match, use default option if available (Priority 3)
            else if (defaultOption && defaultOption.trim() !== '') {
                console.log(`Main-Submit: No value to match, using default option with selector "${defaultOption}"`);
                await clickDefaultOption();
            } else {
                // If no value and no default option, do nothing
                console.log(`Main-Submit: No value to match and no default option found for ${fieldType} field "${field.label || 'Unnamed'}" - doing nothing`);
                // Do nothing - don't count as success or error
            }

            // Skip the rest of the loop for these types
            continue;
        }

        // Helper function to click the default option
        async function clickDefaultOption() {
            try {
                // Use the same approach for both radio and checkbox groups - use setCheckboxRadioState
                console.log(`Main-Submit: Attempting to click ${fieldType} default option with selector "${defaultOption}"`);

                // Use setCheckboxRadioState for both radio and checkbox groups
                const response = await chrome.runtime.sendMessage({
                    action: 'proxyRequestToContentScript',
                    targetTabId: state.currentTabId,
                    contentScriptAction: 'setCheckboxState', // This will be handled by handleSetCheckboxRadioState
                    payload: { selector: defaultOption }
                });

                if (response?.success) {
                    console.log(`Main-Submit: Successfully clicked ${fieldType} default option with selector "${defaultOption}"`);
                    successCount++;
                } else {
                    throw new Error(response?.error || 'Content script failed to click element.');
                }
            } catch (error) {
                errorCount++;
                console.error(`Main-Submit: Click/Set Error for ${fieldType} with defaultOption "${defaultOption}":`, error);
            }
        }

        // Handle select elements
        if (fieldType === 'select' || fieldType === 'custom-select') {
            if (selector) {
                // Note: itemsProcessed will be incremented at the end of the field processing block



                try {
                    // For select and custom-select, if there's no match field value but there is a default value, use that
                    if (!valueToSet && (fieldType === 'select' || fieldType === 'custom-select') && field.value && field.value.trim() !== '') {
                        console.log(`Main-Submit: No match field value for ${fieldType} ${selector}, using default value: "${field.value}"`);
                        valueToSet = field.value;
                    } else if (!valueToSet) {
                        console.log(`Main-Submit: No value to set for ${fieldType} ${selector}`);
                        continue;
                    }

                    console.log(`Main-Submit: Processing select element with selector "${selector}" for value "${valueToSet}"`);

                    // Get the options from the select element
                    let optionsResponse = null;

                    // Check if this is a custom-select and if we have an optionSelector
                    const isCustomSelect = fieldType === 'custom-select';
                    const optionSelector = field.optionSelector || '';

                    console.log(`Main-Submit: Field type is ${fieldType}, isCustomSelect: ${isCustomSelect}, optionSelector: ${optionSelector}`);

                    // Different handling for custom-select vs regular select
                    if (isCustomSelect && optionSelector) {
                        console.log(`Main-Submit: Getting custom-select options`);
                        const payload = {
                            selector: selector,
                            optionSelector: optionSelector,
                            fieldLabel: field.label || ''
                        };

                        console.log(`Main-Submit: Getting custom-select options with payload:`, payload);

                        optionsResponse = await chrome.runtime.sendMessage({
                            action: 'proxyRequestToContentScript',
                            targetTabId: state.currentTabId,
                            contentScriptAction: 'getSelectOptions',
                            payload: payload
                        });
                    } else {
                        // For regular select elements
                        const payload = {
                            selector: selector,
                            fieldLabel: field.label || ''
                        };

                        console.log(`Main-Submit: Getting regular select options with payload:`, payload);

                        optionsResponse = await chrome.runtime.sendMessage({
                            action: 'proxyRequestToContentScript',
                            targetTabId: state.currentTabId,
                            contentScriptAction: 'getSelectOptions',
                            payload: payload
                        });
                    }

                    // Different validation for custom-select vs regular select
                    let matchedOption = null;
                    let customSelectorResult = null;

                    if (isCustomSelect && optionSelector) {
                        if (!optionsResponse?.success || !optionsResponse?.html) {
                            throw new Error(optionsResponse?.error || 'Failed to get HTML from custom-select element after multiple attempts.');
                        }

                        console.log(`Main-Submit: Retrieved HTML from custom-select element:`, optionsResponse.html);

                        // For custom-select, we'll use AI to find the best matching option and get a CSS selector
                        console.log(`Main-Submit: Using AI to find matching option in custom-select HTML`);

                        // Using findCustomSelectOption from sidepanel-ai-service.js
                        customSelectorResult = await findCustomSelectOption(
                            optionsResponse.html,
                            valueToSet,
                            field.label || ''
                        );

                        if (!customSelectorResult || !customSelectorResult.selector) {
                            throw new Error(`No matching option found in custom-select for value "${valueToSet}"`);
                        }

                        console.log(`Main-Submit: AI found matching option with selector: ${customSelectorResult.selector}`);
                        console.log(`Main-Submit: Matched text: "${customSelectorResult.matchedText}" with confidence: ${customSelectorResult.confidence}%`);
                    } else {
                        if (!optionsResponse?.success || !optionsResponse?.options) {
                            throw new Error(optionsResponse?.error || 'Failed to get options from select element after multiple attempts.');
                        }

                        const options = optionsResponse.options;
                        console.log(`Main-Submit: Retrieved ${options.length} options from select element:`, options);

                        // Using findBestMatchingOption from sidepanel-ai-service.js
                        // Pass the field label to improve matching accuracy
                        matchedOption = await findBestMatchingOption(options, valueToSet, field.label || '');

                        if (!matchedOption) {
                            throw new Error(`No matching option found for value "${valueToSet}"`);
                        }
                    }

                    // Select the option - with retry for regular selects, single attempt for custom-select
                    let selectResponse = null;

                    // Different handling for custom-select vs regular select
                    if (isCustomSelect && customSelectorResult) {
                        // For custom-select, we use the AI-generated selector directly with a single attempt
                        try {
                            console.log(`Main-Submit: Using direct click for custom-select option with selector: ${customSelectorResult.selector}`);

                            // Add a small delay before clicking to ensure the dropdown is fully visible
                            await new Promise(resolve => setTimeout(resolve, 500));

                            // Log the HTML of the option element before clicking (for debugging)
                            try {
                                const htmlResponse = await chrome.runtime.sendMessage({
                                    action: 'proxyRequestToContentScript',
                                    targetTabId: state.currentTabId,
                                    contentScriptAction: 'getFullHTML',
                                    payload: { selector: customSelectorResult.selector }
                                });

                                if (htmlResponse?.success && htmlResponse?.html) {
                                    console.log(`Main-Submit: HTML of option element before clicking:`,
                                        htmlResponse.html.length > 200 ?
                                        htmlResponse.html.substring(0, 200) + '...' :
                                        htmlResponse.html);
                                }
                            } catch (htmlError) {
                                console.warn(`Main-Submit: Error getting HTML of option element:`, htmlError);
                            }

                            // For custom-select, use a single direct click attempt
                            // This is not a native <select>, so we don't need multiple attempts
                            console.log(`Main-Submit: Using single click attempt for custom-select option`);
                            selectResponse = await chrome.runtime.sendMessage({
                                action: 'proxyRequestToContentScript',
                                targetTabId: state.currentTabId,
                                contentScriptAction: 'clickElement',
                                payload: { selector: customSelectorResult.selector }
                            });

                            // After successfully selecting the option, wait 0.25s then click the body to close/reset the dropdown
                            if (selectResponse?.success) {
                                console.log(`Main-Submit: Waiting 0.25s after option selection before closing dropdown`);
                                await new Promise(resolve => setTimeout(resolve, 250));

                                console.log(`Main-Submit: Clicking body after option selection to close/reset dropdown`);
                                try {
                                    await chrome.runtime.sendMessage({
                                        action: 'proxyRequestToContentScript',
                                        targetTabId: state.currentTabId,
                                        contentScriptAction: 'clickElement',
                                        payload: { selector: 'body' }
                                    });
                                } catch (bodyClickError) {
                                    console.warn(`Main-Submit: Error clicking body after option selection:`, bodyClickError);
                                    // Continue anyway
                                }
                            }
                        } catch (error) {
                            console.error(`Main-Submit: Error selecting custom-select option:`, error);
                            throw error;
                        }
                    } else {
                        // For regular select
                        const payload = {
                            // For regular select, we use the standard approach
                            selector: selector,
                            value: matchedOption.value
                        };

                        console.log(`Main-Submit: Selecting regular select option with payload:`, payload);

                        selectResponse = await chrome.runtime.sendMessage({
                            action: 'proxyRequestToContentScript',
                            targetTabId: state.currentTabId,
                            contentScriptAction: 'selectOption',
                            payload: payload
                        });
                    }

                    if (selectResponse?.success) {
                        successCount++;
                    } else {
                        throw new Error(selectResponse?.error || 'Failed to select option.');
                    }
                } catch (error) {
                    errorCount++;
                    console.error(`Main-Submit: Select Error for selector ${selector}:`, error);
                }
                // Skip the rest of the loop for select elements
                continue;
            }
        }

        // For other field types, proceed with normal value setting
        // Note: itemsProcessed will be incremented at the end of the field processing block

        // Skip further processing if we don't have a selector
        if (!selector) {
            continue;
        }

        try {
            // Handle file input fields with image URLs
            if (fieldType === 'input-file' || fieldType === 'custom-input-file') {
                // Check if we have a URL value to set
                if (valueToSet && valueToSet.trim() && valueToSet.startsWith('http')) {
                    console.log(`Main-Submit: Attempting to upload image from URL "${valueToSet}" to file input with selector "${selector}"`);

                    try {
                        // Extract filename from URL
                        const urlParts = valueToSet.split('/');
                        const filename = urlParts[urlParts.length - 1].split('?')[0] || 'image.jpg';

                        // Send message to content script to upload file from URL
                        const fileResponse = await chrome.runtime.sendMessage({
                            action: 'proxyRequestToContentScript',
                            targetTabId: state.currentTabId,
                            contentScriptAction: 'uploadFileFromUrl',
                            payload: {
                                selector: selector,
                                imageUrl: valueToSet,
                                filename: filename
                            }
                        });

                        if (fileResponse?.success) {
                            console.log(`Main-Submit: Successfully uploaded image from URL to file input with selector "${selector}"`);
                            successCount++;
                        } else {
                            throw new Error(fileResponse?.error || 'Failed to upload file from URL');
                        }
                    } catch (fileError) {
                        console.error(`Main-Submit: Error uploading file from URL "${valueToSet}" to selector "${selector}":`, fileError);
                        errorCount++;
                    }
                } else {
                    console.log(`Main-Submit: Skipping file input field with selector "${selector}" - no valid image URL provided`);
                    // Mark as processed but not as error
                    successCount++;
                }
            }
            // Handle drag and drop file inputs
            else if (fieldType === 'dragdrop-input-file') {
                // Check if we have a URL value to set
                if (valueToSet && valueToSet.trim() && valueToSet.startsWith('http')) {
                    console.log(`Main-Submit: Attempting to simulate drag and drop of image from URL "${valueToSet}" to element with selector "${selector}"`);

                    try {
                        // Extract filename from URL
                        const urlParts = valueToSet.split('/');
                        const filename = urlParts[urlParts.length - 1].split('?')[0] || 'image.jpg';

                        // Send message to content script to simulate drag and drop
                        const dragDropResponse = await chrome.runtime.sendMessage({
                            action: 'proxyRequestToContentScript',
                            targetTabId: state.currentTabId,
                            contentScriptAction: 'simulateDragDropFile',
                            payload: {
                                selector: selector,
                                imageUrl: valueToSet,
                                filename: filename
                            }
                        });

                        if (dragDropResponse?.success) {
                            console.log(`Main-Submit: Successfully simulated drag and drop of image to element with selector "${selector}"`);
                            successCount++;
                        } else {
                            throw new Error(dragDropResponse?.error || 'Failed to simulate drag and drop');
                        }
                    } catch (dragDropError) {
                        console.error(`Main-Submit: Error simulating drag and drop of image from URL "${valueToSet}" to selector "${selector}":`, dragDropError);
                        errorCount++;
                    }
                } else {
                    console.log(`Main-Submit: Skipping drag-drop file input with selector "${selector}" - no valid image URL provided`);
                    // Mark as processed but not as error
                    successCount++;
                }
            }
            // Handle custom-textarea fields with iframe support
            else if (fieldType === 'custom-textarea') {
                console.log(`Main-Submit: Processing custom-textarea field with selector "${selector}" for iframe handling`);

                try {
                    const response = await chrome.runtime.sendMessage({
                        action: 'proxyRequestToContentScript',
                        targetTabId: state.currentTabId,
                        contentScriptAction: 'setIframeValue',
                        payload: { selector: selector, value: valueToSet }
                    });

                    if (response?.success) {
                        console.log(`Main-Submit: Successfully set iframe value for custom-textarea with selector "${selector}"`);
                        successCount++;
                    } else {
                        console.error(`Main-Submit: Failed to set iframe value for custom-textarea with selector "${selector}":`, response?.error);
                        errorCount++;
                    }
                } catch (error) {
                    console.error(`Main-Submit: Error setting iframe value for custom-textarea:`, error);
                    errorCount++;
                }
            }

            // Skip other custom field types except for custom-radio-group, custom-checkbox-group, custom-select, and custom-textarea
            else if (fieldType.startsWith('custom-') &&
                    fieldType !== 'custom-radio-group' &&
                    fieldType !== 'custom-checkbox-group' &&
                    fieldType !== 'custom-select' &&
                    fieldType !== 'custom-textarea') {
                console.log(`Main-Submit: Skipping custom field type "${fieldType}" with selector "${selector}" - custom fields are not processed`);
                // Mark as processed but not as error
                successCount++;
            }
            // Handle all other input types normally
            else {
                console.log(`Main-Submit: Attempting to set value for selector "${selector}" to "${valueToSet}"`);

                const response = await chrome.runtime.sendMessage({
                    action: 'proxyRequestToContentScript',
                    targetTabId: state.currentTabId,
                    contentScriptAction: 'setElementValue',
                    payload: { selector: selector, value: valueToSet }
                });

                if (response?.success) {
                    successCount++;
                }
                // Special handling for file inputs that were detected by the content script
                else if (response?.isFileInput) {
                    console.log(`Main-Submit: File input detected by content script, skipping`);
                    successCount++;
                }
                else {
                    throw new Error(response?.error || 'Content script failed to set value.');
                }
            }
        } catch (error) {
             errorCount++;
             console.error(`Main-Submit: Match Error for selector ${selector}:`, error);
        }

        itemsProcessed++;
        } // End of field processing
    } // End of for loop

    return { fieldsProcessed: itemsProcessed, successCount, errorCount };
}

// Function to process an action
async function processAction(actionData) {
    console.log('Processing action:', actionData);

    // Apply delay if specified
    if (actionData.delay && actionData.delay > 0) {
        console.log(`Waiting ${actionData.delay}ms before executing action: ${actionData.actionType}`);
        await new Promise(resolve => setTimeout(resolve, actionData.delay));
    }

    switch (actionData.actionType) {
        case 'take-screenshot':
            await executeScreenshotAction();
            break;
        case 'save-report-site-details':
            await executeSaveReportSiteDetailsAction();
            break;
        default:
            console.warn(`Unknown action type: ${actionData.actionType}`);
    }
}

// Execute screenshot action
async function executeScreenshotAction() {
    console.log('Executing take-screenshot action');
    try {
        await handleAddScreenshotClick();
        console.log('Screenshot action completed successfully');
    } catch (error) {
        console.error('Error executing screenshot action:', error);
        throw error;
    }
}

// Execute save report site details action
async function executeSaveReportSiteDetailsAction() {
    console.log('Executing save-report-site-details action');
    try {
        // Check the "Manually Checked" checkbox
        const manuallyCheckedCheckbox = document.getElementById('checked');
        if (manuallyCheckedCheckbox) {
            manuallyCheckedCheckbox.checked = true;
            console.log('Manually Checked checkbox set to true');
        } else {
            console.warn('Manually Checked checkbox not found');
        }

        // Trigger the save report site details action
        await handleSaveReportSiteClick();
        console.log('Save report site details action completed successfully');
    } catch (error) {
        console.error('Error executing save report site details action:', error);
        throw error;
    }
}

async function handleSaveReportSiteClick() {
    console.log('Main-Submit: Save Report Site clicked.');
    if (!state.activeReport?.report_id) {
        alert('Cannot save: No active report selected.');
        return;
    }

    // Check if we have a site ID
    if (!state.currentSiteId) {
        // Try to load site data for the current URL first
        if (state.currentUrl) {
            try {
                console.log('Main-Submit: Attempting to load site data for URL:', state.currentUrl);
                const result = await getSiteJSON(state.currentUrl);
                if (result.status === 'success' && result.site_id) {
                    state.currentSiteId = result.site_id;
                    state.currentSiteUrl = result.site_url;
                    updateUnifiedStatus();
                    console.log('Main-Submit: Successfully retrieved site ID:', state.currentSiteId);
                } else {
                    console.log('Main-Submit: No site found for URL:', state.currentUrl);
                    alert('Cannot save: Site ID not identified for the current page. This URL may not be in the database yet.');
                    return;
                }
            } catch (error) {
                console.error('Main-Submit: Error loading site data:', error);
                alert('Error loading site data: ' + error.message);
                return;
            }
        } else {
            alert('Cannot save: Site ID not identified for the current page.');
            return;
        }
    }

    saveReportSiteButtonMain.disabled = true;
    saveReportSiteButtonMain.textContent = 'Saving...';
    try {
        const reportSiteData = {
            checked: document.getElementById('checked')?.checked || false,
            screenshot_urls: getScreenshotUrls()
        };

        const response = await saveReportSite(state.activeReport.report_id, state.currentSiteId, reportSiteData);

        if (response.status !== 'success') {
            throw new Error(response.message || 'API Error saving report site details');
        }

        alert('Report site details saved successfully!');
        if (response.report_site) {
            populateReportSiteFields(response.report_site);
        } else {
             // If no response data, keep current UI state (screenshots should already be displayed)
             console.log('Main-Submit: No report_site data in response, keeping current UI state');
        }

    } catch (error) {
        console.error('Main-Submit: Error saving report site:', error);
        alert(`Save Error: ${error.message}`);
    } finally {
        saveReportSiteButtonMain.disabled = false;
        saveReportSiteButtonMain.textContent = 'Save Report Site Details';
    }
}



async function handleImageUrlUploadButtonClick(button) {
    const inputField = button.closest('.field-group-with-button')?.querySelector('input[type="text"]');

    if (!inputField) {
        console.error("Main-Submit: Could not find input field for image URL upload button.");
        alert("Error: Could not link button to input field.");
        return;
    }

    // Get the current value from the input field (should be an image URL)
    const imageUrl = inputField.value.trim();
    if (!imageUrl) {
        alert("Please enter an image URL first.");
        return;
    }

    // Find the field container to get the selector
    const fieldContainer = button.closest('.dynamic-field-container');
    if (!fieldContainer) {
        console.error("Main-Submit: Could not find field container.");
        alert("Error: Could not find field container.");
        return;
    }

    // Get the selector from the field container
    const selectorInput = fieldContainer.querySelector('.selector-input');
    if (!selectorInput || !selectorInput.value.trim()) {
        console.error("Main-Submit: Could not find selector input or selector is empty.");
        alert("Error: No selector found for this field. Please configure a selector first.");
        return;
    }

    const selector = selectorInput.value.trim();
    const fieldName = inputField.name || inputField.id || 'image';
    console.log(`Main-Submit: Image URL upload requested for ${fieldName} with selector "${selector}": ${imageUrl}`);

    // Disable button and show loading state
    button.disabled = true;
    button.innerHTML = '...';

    try {
        // Extract filename from URL
        const urlParts = imageUrl.split('/');
        const filename = urlParts[urlParts.length - 1].split('?')[0] || 'image.jpg';

        console.log(`Main-Submit: Uploading image from URL to file input with selector "${selector}"`);

        // Send message to content script to upload file from URL
        const response = await chrome.runtime.sendMessage({
            action: 'proxyRequestToContentScript',
            targetTabId: state.currentTabId,
            contentScriptAction: 'uploadFileFromUrl',
            payload: {
                selector: selector,
                imageUrl: imageUrl,
                filename: filename
            }
        });

        if (!response?.success) {
            throw new Error(response?.error || 'Failed to upload file from URL');
        }

        console.log(`Main-Submit: Successfully uploaded image from URL to file input`);
        alert(`Image from URL for ${fieldName} uploaded successfully to the file input.`);

    } catch (error) {
        console.error(`Main-Submit: Image URL Upload Error (${fieldName}):`, error);
        alert(`Image URL Upload Error: ${error.message}`);
    } finally {
        button.disabled = false;
        button.innerHTML = '<svg><use xlink:href="#icon-link"></use></svg>';
    }
}

// New screenshot handling functions
async function handleAddScreenshotClick() {
    const button = document.getElementById('add-screenshot-button');

    if (!state.currentTabId) {
        alert("Cannot take screenshot: Active tab not identified.");
        return;
    }

    console.log('Main-Submit: Adding new screenshot...');
    button.disabled = true;
    button.innerHTML = '...';

    try {
        console.log('Main-Submit: Capturing visible tab in current window...');
        const screenshotDataUrl = await chrome.tabs.captureVisibleTab(null, { format: 'png' });

        if (!screenshotDataUrl) {
            throw new Error("Tab capture returned empty data.");
        }
        console.log(`Main-Submit: Capture successful, data length: ${screenshotDataUrl.length}`);
        button.innerHTML = 'Up..';

        const uploadResponse = await uploadImage(screenshotDataUrl);
        if (uploadResponse.status !== 'success' || !uploadResponse.image_url) {
            throw new Error(uploadResponse.message || 'Image upload failed or returned no URL.');
        }
        console.log(`Main-Submit: Upload successful, URL: ${uploadResponse.image_url}`);

        addScreenshotToDisplay(uploadResponse.image_url);
        alert('Screenshot captured and uploaded successfully!');

    } catch (error) {
        console.error('Main-Submit: Screenshot Error:', error);
        alert(`Screenshot Error: ${error.message}`);
    } finally {
        button.disabled = false;
        button.innerHTML = '<svg><use xlink:href="#icon-camera"></use></svg>';
    }
}

function addScreenshotToDisplay(imageUrl) {
    const screenshotsDisplay = document.getElementById('screenshots-display');

    const thumbnailDiv = document.createElement('div');
    thumbnailDiv.className = 'screenshot-thumbnail';
    thumbnailDiv.setAttribute('data-url', imageUrl);

    const img = document.createElement('img');
    img.src = imageUrl;
    img.alt = 'Screenshot';

    const removeButton = document.createElement('button');
    removeButton.className = 'remove-screenshot';
    removeButton.title = 'Remove screenshot';

    thumbnailDiv.appendChild(img);
    thumbnailDiv.appendChild(removeButton);
    screenshotsDisplay.appendChild(thumbnailDiv);
}

function handleRemoveScreenshotClick(button) {
    const thumbnail = button.closest('.screenshot-thumbnail');
    if (thumbnail) {
        thumbnail.remove();
        console.log('Main-Submit: Screenshot removed from display');
    }
}

function getScreenshotUrls() {
    const thumbnails = document.querySelectorAll('.screenshot-thumbnail');
    const urls = [];
    thumbnails.forEach(thumbnail => {
        const url = thumbnail.getAttribute('data-url');
        if (url) {
            urls.push(url);
        }
    });
    return urls;
}

function clearScreenshotsDisplay() {
    const screenshotsDisplay = document.getElementById('screenshots-display');
    screenshotsDisplay.innerHTML = '';
}

function populateScreenshotsDisplay(screenshotUrls) {
    clearScreenshotsDisplay();
    if (screenshotUrls && Array.isArray(screenshotUrls)) {
        screenshotUrls.forEach(url => {
            if (url && url.trim()) {
                addScreenshotToDisplay(url.trim());
            }
        });
    }
}

console.log("sidepanel-main-submit.js loaded");