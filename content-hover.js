// content-hover.js - Page element hover detection functionality

// Function to set up page element hover listeners
function setupPageElementHoverListeners() {
    // Create a map to cache selector matches for better performance
    const selectorElementMap = new Map();

    // Function to find matching selector for an element
    function findMatchingSelector(element) {
        // Skip if no selectors or element is null
        if (!element || window.knownSelectors.size === 0) return null;

        // Skip our UI elements
        if (element.id === 'form-analyzer-highlight' ||
            element.closest('#form-analyzer-highlight')) {
            return null;
        }

        // Check each selector
        for (const selector of window.knownSelectors) {
            try {
                // Use cached elements if available
                let elements = selectorElementMap.get(selector);
                if (!elements) {
                    elements = Array.from(document.querySelectorAll(selector));
                    selectorElementMap.set(selector, elements);
                }

                // Check if our element is in the list
                if (elements.includes(element)) {
                    return selector;
                }
            } catch (error) {
                // Invalid selector, ignore
                console.warn(`Invalid selector in hover detection: ${selector}`, error);
            }
        }
        return null;
    }

    // Handle mouseover event
    document.addEventListener('mouseover', (e) => {
        // Skip if we're in selection mode
        if (window.selectionModeActive) return;

        const matchingSelector = findMatchingSelector(e.target);
        if (matchingSelector) {

            // Highlight the element
            if (typeof window.highlightElement === 'function') {
                window.highlightElement(e.target, true, false);
            }

            // Send message to sidepanel
            chrome.runtime.sendMessage({
                action: 'pageElementHovered',
                selector: matchingSelector
            }).catch(error => console.error('Error sending pageElementHovered message:', error));
        }
    });

    // Handle mouseout event
    document.addEventListener('mouseout', (e) => {
        // Skip if we're in selection mode
        if (window.selectionModeActive) return;

        const matchingSelector = findMatchingSelector(e.target);
        if (matchingSelector) {
            // Remove highlight
            if (typeof window.removeHighlight === 'function') {
                window.removeHighlight();
            }
        }
    });

    // Clear the cache when selectors are updated
    document.addEventListener('selectorsCacheInvalidated', () => {
        selectorElementMap.clear();
    });

}

// Make the function available globally
window.setupPageElementHoverListeners = setupPageElementHoverListeners;
