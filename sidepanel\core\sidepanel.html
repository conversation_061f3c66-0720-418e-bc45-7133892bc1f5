<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>Form Analyzer</title>
  <!-- CSS Includes -->
  <link rel="stylesheet" href="../../lib/jquery-ui.min.css">
  <link rel="stylesheet" href="./sidepanel.css">
  <link rel="stylesheet" href="../common/element-validator.css">
  <style>
    /* Keep only essential layout styles here */
    html, body { height: 100%; margin: 0; padding: 0; display: flex; flex-direction: column; overflow: hidden; font-size: 14px; }
    #tabs { flex-grow: 1; border: none; padding: 0; display: flex; flex-direction: column; background-color: #fff; overflow: hidden; }
    #tabs .ui-tabs-nav { padding: 5px 10px 0; background: #f8f9fa; border-bottom: 1px solid #dee2e6; border-radius: 0; margin: 0; flex-shrink: 0; }
    #tabs .ui-tabs-nav li { margin: 0 5px -1px 0; border-radius: 4px 4px 0 0; border: 1px solid #dee2e6; background-color: #e9ecef; }
    #tabs .ui-tabs-nav li.ui-tabs-active { background-color: #fff; border-bottom-color: transparent; }
    #tabs .ui-tabs-nav .ui-tabs-anchor { padding: 8px 15px; font-weight: bold; color: #495057; outline: none; font-size: 13px; }
    #tabs .ui-tabs-nav li.ui-tabs-active .ui-tabs-anchor { color: #007bff; }
    #tabs .ui-tabs-panel { padding: 15px; flex-grow: 1; overflow-y: auto; border-width: 0; background-color: #ffffff; }

    /* Tab Specific Controls */
    .tab-controls { display: flex; gap: 8px; margin-bottom: 15px; align-items: center; justify-content: space-between; }
    .tab-controls button { flex: none; padding: 6px 12px; font-size: 12px; line-height: 1; margin: 0; } /* Base style */

    /* Admin Tab Button Styles */
    #admin-tab #saveJsonButton { color: #4CAF50; border: 1px solid #4CAF50; background: none; }
    #admin-tab #saveJsonButton:hover { background-color: #e8f5e9; }
    #admin-tab #saveJsonButton:disabled { color: #ccc; border-color: #ccc; background: none; cursor: not-allowed;}

    /* Submit Tab Button Styles */
    #submit-tab #saveReportSiteButton {
        font-weight: bold;
        margin-top: 15px;
        background-color: transparent !important;
        color: #007bff !important;
        border: 1px solid #007bff !important;
    }
    #submit-tab #saveReportSiteButton:hover:not(:disabled) {
        background-color: #007bff !important;
        color: white !important;
        border-color: #007bff !important;
    }

    /* Match & Fill Button Styles */
    .match-page-button {
        background-color: #007bff !important;
        color: white !important;
        border: 1px solid #007bff !important;
        font-weight: bold;
    }
    .match-page-button:hover:not(:disabled) {
        background-color: #0056b3 !important;
        border-color: #0056b3 !important;
    }

    /* Submit Tab Specific Layout */
     #submit-tab h4.section-header, #tools-tab h4.section-header { /* Style for the static headers */
        font-size: 14px;
        font-weight: bold;
        padding-bottom: 8px;
        margin-top: 15px;
        margin-bottom: 10px;
        border-bottom: 1px solid #eee;
        color: #333;
     }
     #submit-tab h4.section-header:first-of-type, #tools-tab h4.section-header:first-of-type {
         margin-top: 0; /* Remove top margin for the very first header */
     }

    /* Tools Tab Button Styles */
    #tools-tab .click-button {
        background-color: #4CAF50;
        color: white;
        border: none;
        padding: 6px 12px;
        cursor: pointer;
    }
    #tools-tab .click-button:hover {
        background-color: #45a049;
    }
    #tools-tab .convert-button {
        background-color: #2196F3;
        color: white;
        border: none;
        padding: 6px 12px;
        cursor: pointer;
    }
    #tools-tab .convert-button:hover {
        background-color: #0b7dda;
    }

    /* Status Bar */
    #unified-status-bar { display: flex; flex-direction: column; padding: 6px 15px; background-color: #f8f9fa; border-top: 1px solid #dee2e6; font-size: 11px; color: #6c757d; flex-shrink: 0; font-family: monospace; }
    #status-site-url, #status-report-id { font-weight: normal; margin: 1px 0; word-wrap: break-word; }

    /* Loading Placeholder Styling */
    .loading-placeholder {
      text-align: center;
      padding: 20px;
      color: #6c757d;
      font-style: italic;
      border: 1px dashed #dee2e6;
      border-radius: 4px;
      background-color: #f8f9fa;
      margin-top: 10px; /* Adjust as needed */
    }

    /* Page Accordion Styling */
    .page-accordion {
      margin-bottom: 15px;
      border: 1px solid #dee2e6;
      border-radius: 4px;
    }

    .page-accordion-header {
      background-color: #f8f9fa;
      padding: 10px;
      font-weight: bold;
      cursor: pointer;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .page-accordion-content {
      padding: 10px;
      border-top: 1px solid #dee2e6;
    }

    .page-controls {
      display: flex;
      gap: 5px;
      margin-bottom: 10px;
    }

    .page-controls button {
      background-color: #007bff;
      color: #fff;
      padding: 4px 6px;
      font-size: 12px;
    }

    .page-controls button:hover {
      background-color: #0056b3;
    }

    /* Add Action button styling */
    .add-action-button {
      background-color: #28a745 !important;
      color: white !important;
      border: 1px solid #28a745 !important;
    }

    .add-action-button:hover {
      background-color: #218838 !important;
      border-color: #218838 !important;
    }

  </style>
</head>
<body>

  <div id="tabs">
    <ul>
      <li><a href="#admin-tab">Admin</a></li>
      <li><a href="#submit-tab">Submit</a></li>
      <li><a href="#tools-tab">Tools</a></li>
    </ul>

    <!-- Admin Tab Content -->
    <div id="admin-tab">
      <div class="tab-controls">
        <button id="saveJsonButton" title="Save the current form configuration for this site's URL">Save Site JSON</button>
        <button id="addPageButton" class="secondary" title="Add a new page accordion">+ Add Page</button>
      </div>
      <!-- Content area for admin fields accordion -->
      <div id="admin-form-fields-content">
        <p class="loading-placeholder">Loading configuration...</p> <!-- Use class -->
      </div>

      <!-- Status checkboxes section -->
      <div class="field-group" style="margin-top: 15px; padding: 10px; border: 1px solid #ddd; border-radius: 4px; background-color: #f9f9f9;">
        <div style="display: flex; gap: 20px; align-items: center;">
          <div class="checkbox-field">
            <input type="checkbox" id="configurationReady" name="configurationReady">
            <label for="configurationReady" style="font-weight: bold;">Ready</label>
          </div>
          <div class="checkbox-field">
            <input type="checkbox" id="configurationReviewed" name="configurationReviewed">
            <label for="configurationReviewed" style="font-weight: bold;">Reviewed</label>
          </div>
        </div>
        <p style="font-size: 12px; color: #666; margin: 5px 0 0 0;">Check "Ready" when fields are configured correctly. Check "Reviewed" when configuration has been verified.</p>
      </div>
    </div>

    <!-- Submit Tab Content -->
    <div id="submit-tab">
       <!-- Report Site Details (No longer in an accordion section) -->
       <h4 class="section-header">Report Site Details</h4>
       <div class="field-group"> <!-- Use field-group for styling -->
         <div class="checkbox-field">
           <input type="checkbox" id="checked" name="checked">
           <label for="checked">Manually Checked</label>
         </div>
       </div>
       <div class="field-group"> <!-- Screenshots section -->
         <label>Screenshots</label>
         <div class="screenshots-container">
           <button id="add-screenshot-button" class="screenshot-capture-button" title="Take Screenshot of Current View">
             <svg><use xlink:href="#icon-camera"></use></svg>
           </button>
           <div id="screenshots-display" class="screenshots-display">
             <!-- Screenshot thumbnails will be added here dynamically -->
           </div>
         </div>
       </div>
       <div class="button-container">
         <button id="saveReportSiteButton" class="primary">Save Report Site Details</button>
       </div>

       <!-- Form Fields & Values (No longer in an accordion section) -->
       <h4 class="section-header">Form Fields & Values</h4>
       <!-- Content area for submit fields accordion -->
       <div id="submit-form-fields-content">
          <p class="loading-placeholder">Loading fields...</p> <!-- Use class -->
       </div>

    </div>

    <!-- Tools Tab Content -->
    <div id="tools-tab">
      <!-- Content will be dynamically generated by sidepanel-ui-tools.js -->
    </div>
  </div>

  <!-- Unified Status Bar -->
  <div id="unified-status-bar">
    <div id="status-site-url">Site URL: N/A</div>
    <div id="status-report-id">Report ID: N/A</div>
  </div>

  <!-- SVG Icons (Keep as is) -->
  <svg style="display:none">
    <symbol id="icon-delete" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512"><path d="M135.2 17.7C140.6 6.8 151.7 0 163.8 0L284.2 0c12.1 0 23.2 6.8 28.6 17.7L320 32l96 0c17.7 0 32 14.3 32 32s-14.3 32-32 32L32 96C14.3 96 0 81.7 0 64S14.3 32 32 32l96 0 7.2-14.3zM32 128l384 0 0 320c0 35.3-28.7 64-64 64L96 512c-35.3 0-64-28.7-64-64l0-320zm96 64c-8.8 0-16 7.2-16 16l0 224c0 8.8 7.2 16 16 16s16-7.2 16-16l0-224c0-8.8-7.2-16-16-16zm96 0c-8.8 0-16 7.2-16 16l0 224c0 8.8 7.2 16 16 16s16-7.2 16-16l0-224c0-8.8-7.2-16-16-16zm96 0c-8.8 0-16 7.2-16 16l0 224c0 8.8 7.2 16 16 16s16-7.2 16-16l0-224c0-8.8-7.2-16-16-16z" /></symbol>
    <symbol id="icon-generate" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><path d="M495.9 166.6c3.2 8.7 .5 18.4-6.4 24.6l-43.3 39.4c1.1 8.3 1.7 16.8 1.7 25.4s-.6 17.1-1.7 25.4l43.3 39.4c6.9 6.2 9.6 15.9 6.4 24.6c-4.4 11.9-9.7 23.3-15.8 34.3l-4.7 8.1c-6.6 11-14 21.4-22.1 31.2c-5.9 7.2-15.7 9.6-24.5 6.8l-55.7-17.7c-13.4 10.3-28.2 18.9-44 25.4l-12.5 57.1c-2 9.1-9 16.3-18.2 17.8c-13.8 2.3-28 3.5-42.5 3.5s-28.7-1.2-42.5-3.5c-9.2-1.5-16.2-8.7-18.2-17.8l-12.5-57.1c-15.8-6.5-30.6-15.1-44-25.4L83.1 425.9c-8.8 2.8-18.6 .3-24.5-6.8c-8.1-9.8-15.5-20.2-22.1-31.2l-4.7-8.1c-6.1-11-11.4-22.4-15.8-34.3c-3.2-8.7-.5-18.4 6.4-24.6l43.3-39.4C64.6 273.1 64 264.6 64 256s.6-17.1 1.7-25.4L22.4 191.2c-6.9-6.2-9.6-15.9-6.4-24.6c4.4-11.9 9.7-23.3 15.8-34.3l4.7-8.1c6.6-11 14-21.4 22.1-31.2c5.9-7.2 15.7-9.6 24.5-6.8l55.7 17.7c13.4-10.3 28.2-18.9 44-25.4l12.5-57.1c2-9.1 9-16.3 18.2 17.8C227.3 1.2 241.5 0 256 0s28.7 1.2 42.5 3.5c9.2 1.5 16.2 8.7 18.2 17.8l12.5 57.1c15.8 6.5 30.6 15.1 44 25.4l55.7-17.7c8.8-2.8 18.6-.3 24.5 6.8c8.1 9.8 15.5 20.2 22.1 31.2l4.7 8.1c6.1 11 11.4 22.4 15.8 34.3zM256 336a80 80 0 1 0 0-160 80 80 0 1 0 0 160z" /></symbol>
    <symbol id="icon-save" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512"><path d="M64 32C28.7 32 0 60.7 0 96L0 416c0 35.3 28.7 64 64 64l320 0c35.3 0 64-28.7 64-64l0-242.7c0-17-6.7-33.3-18.7-45.3L352 50.7C340 38.7 323.7 32 306.7 32L64 32zm0 96c0-17.7 14.3-32 32-32l192 0c17.7 0 32 14.3 32 32l0 64c0 17.7-14.3 32-32 32L96 224c-17.7 0-32-14.3-32-32l0-64zM224 288a64 64 0 1 1 0 128 64 64 0 1 1 0-128z"/></symbol>
    <symbol id="icon-camera" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><path d="M149.1 64.8L138.7 96H64C28.7 96 0 124.7 0 160V416c0 35.3 28.7 64 64 64H448c35.3 0 64-28.7 64-64V160c0-35.3-28.7-64-64-64H373.3L362.9 64.8C356.4 45.2 338.1 32 317.4 32H194.6c-20.7 0-39 13.2-45.5 32.8zM256 384c-53 0-96-43-96-96s43-96 96-96s96 43 96 96s-43 96-96 96z"/></symbol>
    <symbol id="icon-link" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 640 512"><path d="M579.8 267.7c56.5-56.5 56.5-148 0-204.5c-50-50-128.8-56.5-186.3-15.4l-1.6 1.1c-14.4 10.3-17.7 30.3-7.4 44.6s30.3 17.7 44.6 7.4l1.6-1.1c32.1-22.9 76-19.3 103.8 8.6c31.5 31.5 31.5 82.5 0 114L422.3 334.8c-31.5 31.5-82.5 31.5-114 0c-27.9-27.9-31.5-71.8-8.6-103.8l1.1-1.6c10.3-14.4 6.9-34.4-7.4-44.6s-34.4-6.9-44.6 7.4l-1.1 1.6C206.5 251.2 213 330 263 380c56.5 56.5 148 56.5 204.5 0L579.8 267.7zM60.2 244.3c-56.5 56.5-56.5 148 0 204.5c50 50 128.8 56.5 186.3 15.4l1.6-1.1c14.4-10.3 17.7-30.3 7.4-44.6s-30.3-17.7-44.6-7.4l-1.6 1.1c-32.1 22.9-76 19.3-103.8-8.6C74 372 74 321 105.5 289.5L217.7 177.2c31.5-31.5 82.5-31.5 114 0c27.9 27.9 31.5 71.8 8.6 103.9l-1.1 1.6c-10.3 14.4-6.9 34.4 7.4 44.6s34.4 6.9 44.6-7.4l1.1-1.6C433.5 260.8 427 182 377 132c-56.5-56.5-148-56.5-204.5 0L60.2 244.3z"/></symbol>
    <symbol id="icon-copy" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512"><path d="M208 0L332.1 0c12.7 0 24.9 5.1 33.9 14.1l67.9 67.9c9 9 14.1 21.2 14.1 33.9L448 336c0 26.5-21.5 48-48 48l-192 0c-26.5 0-48-21.5-48-48l0-288c0-26.5 21.5-48 48-48zM48 128l80 0 0 64-64 0 0 256 192 0 0-32 64 0 0 48c0 26.5-21.5 48-48 48L48 512c-26.5 0-48-21.5-48-48L0 176c0-26.5 21.5-48 48-48z"/></symbol>
  </svg>

  <!-- Library Scripts -->
  <script src="../../lib/jquery.min.js"></script>
  <script src="../../lib/jquery-ui.min.js"></script>

  <!-- Utility and API Scripts -->
  <script src="../common/utils.js"></script>
  <script src="../../lib/api-service.js"></script>
  <script src="../common/click-utils.js"></script>

  <!-- New Utility Modules -->
  <script src="../common/sidepanel-html-utils.js"></script>
  <script src="../common/sidepanel-ai-service.js"></script>

  <!-- Sidepanel UI Modules -->
  <script src="../common/sidepanel-ui-common.js"></script>
  <!-- Admin UI Split Modules (replacing sidepanel-ui-admin.js) -->
  <script src="../admin/sidepanel-ui-admin-utils.js"></script>
  <script src="../admin/sidepanel-ui-admin-selector.js"></script>
  <script src="../admin/sidepanel-ui-admin-renderer.js"></script>
  <script src="../admin/sidepanel-ui-admin-interactions.js"></script>
  <script src="../admin/sidepanel-ui-admin-form-manager.js"></script>
  <script src="../admin/sidepanel-ui-admin-events.js"></script>
  <script src="../submit/sidepanel-ui-submit.js"></script>
  <script src="../tools/xpath-to-css.js"></script>
  <script src="../tools/sidepanel-ui-tools.js"></script>

  <!-- Sidepanel Logic Modules -->
  <script src="../common/sidepanel-logic-common.js"></script>
  <script src="../admin/sidepanel-logic-admin.js"></script>
  <script src="../submit/sidepanel-logic-submit.js"></script>
  <script src="../tools/sidepanel-logic-tools.js"></script>

  <!-- Sidepanel Main Modules (Event listeners setup LAST, after functions are defined) -->
  <script src="../admin/sidepanel-main-admin.js"></script>
  <script src="../submit/sidepanel-main-submit.js"></script>
  <script src="../tools/sidepanel-main-tools.js"></script>
  <script src="../common/sidepanel-main-common.js"></script> <!-- This initializes everything -->

</body>
</html>