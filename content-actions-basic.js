// content-actions-basic.js - Basic element manipulation functions
// Handles: setElementValue, clickElement, setCheckboxState, setRadioState

// Handle setting a value on an element
function handleSetElementValue(message, sendResponse) {
    const { selector: targetSelector, value: targetValue } = message.payload || {};
    console.log(`Attempting to set value for "${targetSelector}" to "${targetValue}"`);

    if (targetSelector && targetValue !== undefined) {
        try {
            const element = document.querySelector(targetSelector);
            if (element) {
                // Special handling for checkbox inputs
                if (element.tagName === 'INPUT' && element.type === 'checkbox') {
                    // Convert various truthy/falsy values to boolean
                    const shouldCheck = targetValue === true ||
                                        targetValue === 'true' ||
                                        targetValue === 'yes' ||
                                        targetValue === '1' ||
                                        targetValue === 'checked' ||
                                        targetValue === 'on';

                    console.log(`Setting checkbox checked state to: ${shouldCheck}`);
                    element.checked = shouldCheck;

                    // Dispatch appropriate events
                    ['input', 'change', 'click'].forEach(eventType => {
                        element.dispatchEvent(new Event(eventType, { bubbles: true, cancelable: true }));
                    });
                    console.log(`Checkbox state set successfully for ${targetSelector}`);
                    sendResponse({ success: true });
                }
                // Special handling for file inputs - we can't set values directly
                else if (element.tagName === 'INPUT' && element.type === 'file') {
                    console.log(`Cannot set value for file input ${targetSelector} - browser security restriction`);
                    sendResponse({
                        success: false,
                        error: 'Cannot set value for file input due to browser security restrictions. File inputs must be handled manually.',
                        isFileInput: true
                    });
                }
                // Handle normal inputs and contentEditable elements
                else if ('value' in element || element.isContentEditable) {
                    if (element.isContentEditable) {
                        element.textContent = targetValue;
                    } else {
                        element.value = targetValue;
                    }
                    ['input', 'change', 'blur', 'keyup', 'keydown'].forEach(eventType => {
                        element.dispatchEvent(new Event(eventType, { bubbles: true, cancelable: true }));
                    });
                    console.log(`Value set successfully for ${targetSelector}`);
                    sendResponse({ success: true });
                } else {
                    console.warn(`Element found for "${targetSelector}", but it doesn't seem to be a standard input/textarea/select or contentEditable.`);
                    sendResponse({ success: false, error: 'Element not a standard input/textarea/select or contentEditable.' });
                }
            } else {
                console.warn(`Element not found for selector: "${targetSelector}"`);
                sendResponse({ success: false, error: 'Element not found.' });
            }
        } catch (e) {
            console.error(`Error in setElementValue for selector "${targetSelector}":`, e);
            sendResponse({ success: false, error: `Execution error: ${e.message}` });
        }
    } else {
        sendResponse({ success: false, error: 'Missing selector or value for setElementValue.' });
    }
}

// Handle clicking an element
function handleClickElement(message, sendResponse) {
    const clickSelector = message.payload?.selector;
    const delaySeconds = message.payload?.delay;
    console.log(`Attempting to click element with selector: "${clickSelector}"${delaySeconds ? ` with ${delaySeconds}s delay` : ''}`);

    if (clickSelector) {
        try {
            const element = document.querySelector(clickSelector);
            if (element) {
                // Function to perform the actual click
                const performClick = () => {
                    // Special handling for checkboxes and radio buttons
                    if (element.tagName === 'INPUT' && (element.type === 'checkbox' || element.type === 'radio')) {
                        console.log(`Element is a ${element.type}. Current checked state: ${element.checked}`);

                        // Force the checked state to true
                        element.checked = true;

                        // Dispatch all relevant events
                        ['input', 'change', 'click'].forEach(eventType => {
                            element.dispatchEvent(new Event(eventType, {
                                bubbles: true,
                                cancelable: true
                            }));
                        });

                        console.log(`${element.type} checked state forcefully set to: ${element.checked}`);
                        sendResponse({ success: true });
                    }
                    // Standard click handling for other elements
                    else {
                        // Simulate a click event
                        element.click();

                        // Also dispatch a more complete set of events for better compatibility
                        ['mousedown', 'mouseup', 'click'].forEach(eventType => {
                            element.dispatchEvent(new MouseEvent(eventType, {
                                bubbles: true,
                                cancelable: true,
                                view: window
                            }));
                        });

                        console.log(`Click simulated successfully on ${clickSelector}`);
                        sendResponse({ success: true });
                    }
                };

                // If a delay is specified, wait before clicking
                if (delaySeconds && !isNaN(delaySeconds)) {
                    const delayMs = parseInt(delaySeconds) * 1000;
                    console.log(`Delaying click for ${delaySeconds} seconds (${delayMs}ms)`);

                    // Return true to indicate we'll send a response asynchronously
                    setTimeout(performClick, delayMs);
                    return true;
                } else {
                    // No delay, perform click immediately
                    performClick();
                }
            } else {
                console.warn(`Element not found for selector: "${clickSelector}"`);
                sendResponse({ success: false, error: 'Element not found.' });
            }
        } catch (e) {
            console.error(`Error in clickElement for selector "${clickSelector}":`, e);
            sendResponse({ success: false, error: `Execution error: ${e.message}` });
        }
    } else {
        sendResponse({ success: false, error: 'Missing selector for clickElement.' });
    }
}

// Handle setting a checkbox or radio button state - simplified to just click the element
function handleSetCheckboxRadioState(message, sendResponse) {
    const { selector } = message.payload || {};
    console.log(`Attempting to click checkbox/radio with selector: "${selector}"`);

    if (selector) {
        try {
            // Log more details to help diagnose the issue
            console.log(`Looking for element with selector: "${selector}"`);
            const element = document.querySelector(selector);

            if (element) {
                console.log(`Element found: ${element.tagName}, type: ${element.type}, id: ${element.id}, class: ${element.className}`);

                // Simply click the element
                element.click();

                // Also dispatch events to ensure the click is registered
                ['input', 'change', 'click'].forEach(eventType => {
                    element.dispatchEvent(new Event(eventType, {
                        bubbles: true,
                        cancelable: true
                    }));
                });

                console.log(`Successfully clicked element with selector: "${selector}"`);
                sendResponse({ success: true });
            } else {
                console.warn(`Element not found for selector: "${selector}"`);
                sendResponse({ success: false, error: 'Element not found.' });
            }
        } catch (e) {
            console.error(`Error clicking element with selector "${selector}":`, e);
            sendResponse({ success: false, error: `Execution error: ${e.message}` });
        }
    } else {
        sendResponse({ success: false, error: 'Missing selector.' });
    }
}

// Make the functions available globally
window.handleSetElementValue = handleSetElementValue;
window.handleClickElement = handleClickElement;
window.handleSetCheckboxRadioState = handleSetCheckboxRadioState;

console.log('Content-Actions-Basic: Basic element manipulation functions loaded');
