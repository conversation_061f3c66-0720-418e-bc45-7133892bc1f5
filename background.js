

const tabUrls = new Map();
let lastActiveTabId = null;

chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
    if (tabs && tabs.length > 0) {
        lastActiveTabId = tabs[0].id;
        if (tabs[0].url && !tabs[0].url.startsWith('chrome://')) {
            tabUrls.set(lastActiveTabId, tabs[0].url);
        }
    }
});


chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
    const isRelevantUpdate = changeInfo.url || (changeInfo.status === 'complete' && tab.url && !tab.url.startsWith('chrome://'));

    if (isRelevantUpdate && tab.url) {
        const oldUrl = tabUrls.get(tabId);
        const newUrl = tab.url;
        if (newUrl !== oldUrl) {
            tabUrls.set(tabId, newUrl);
             if (tabId === lastActiveTabId) {
                 notifySidePanelOfUrlChange(tabId, newUrl, oldUrl);
             }
        } else if (changeInfo.status === 'complete' && tabId === lastActiveTabId) {

        }
    }
});

chrome.tabs.onActivated.addListener(activeInfo => {
    const newlyActivatedTabId = activeInfo.tabId;
    lastActiveTabId = newlyActivatedTabId;

    chrome.tabs.get(lastActiveTabId)
        .then(tab => {
            if (tab?.url && !tab.url.startsWith('chrome://')) {
                const oldUrl = tabUrls.get(lastActiveTabId);
                const newUrl = tab.url;
                if (newUrl !== oldUrl) {
                    tabUrls.set(lastActiveTabId, newUrl);
                }
                notifySidePanelOfUrlChange(lastActiveTabId, newUrl, oldUrl);
            } else {
                notifySidePanelOfUrlChange(lastActiveTabId, null, tabUrls.get(lastActiveTabId));
            }
        })
        .catch(e => console.warn(`Error getting activated tab info for tab ${lastActiveTabId}:`, e));
});

chrome.tabs.onRemoved.addListener((tabId, removeInfo) => {
    tabUrls.delete(tabId);
    if (lastActiveTabId === tabId) {
        lastActiveTabId = null;
        notifySidePanel({ action: 'activeTabClosed' });
    }
});


async function notifySidePanel(message) {
     try {
         await chrome.runtime.sendMessage(message);
     } catch (error) {
         if (error.message?.includes('Receiving end does not exist')) {

         } else {
             console.warn(`Error sending message to side panel (${message.action}): ${error.message}`);
         }
     }
}

async function notifySidePanelOfUrlChange(tabId, newUrl, oldUrl) {
    await notifySidePanel({
        action: 'urlChanged',
        tabId: tabId,
        newUrl: newUrl,
        oldUrl: oldUrl
    });
}

chrome.runtime.onInstalled.addListener(() => {
    chrome.contextMenus.create({
        id: "open-form-analyzer",
        title: "Open Form Analyzer",
        contexts: ["page"]
    });
});

chrome.contextMenus.onClicked.addListener((info, tab) => {
    if (info.menuItemId === "open-form-analyzer" && tab?.id) {
        chrome.sidePanel.open({ tabId: tab.id })
            .catch(e => console.error("Error opening side panel via context menu:", e));
    }
});

// Function to ensure content scripts are loaded in a tab
async function ensureContentScriptsLoaded(tabId) {

    try {
        // Check if the tab exists and is accessible
        let tab;
        try {
            tab = await chrome.tabs.get(tabId);
            if (!tab) {
                console.warn(`Tab ${tabId} does not exist`);
                return false;
            }
        } catch (error) {
            console.warn(`Error getting tab ${tabId}: ${error.message}`);
            return false;
        }

        // Check if the tab is in a state where we can inject scripts
        if (!tab.url || tab.url.startsWith('chrome://') || tab.url.startsWith('about:')) {
            console.warn(`Cannot inject content scripts into Tab ${tabId} with URL ${tab.url}`);
            return false;
        }

        // Check if the tab is loading
        if (tab.status === 'loading') {
            console.log(`Tab ${tabId} is still loading, waiting briefly...`);
            // Wait a short time for the tab to finish loading
            await new Promise(resolve => setTimeout(resolve, 500));

            // Check again
            try {
                tab = await chrome.tabs.get(tabId);
                if (tab.status === 'loading') {
                    console.warn(`Tab ${tabId} is still loading after waiting`);
                    return false;
                }
            } catch (error) {
                console.warn(`Error re-checking tab ${tabId}: ${error.message}`);
                return false;
            }
        }

        // First, check if the content script is already loaded by sending a ping
        const pingResult = await chrome.tabs.sendMessage(tabId, { action: 'ping' })
            .catch(error => {
                // If we get an error, the content script is not loaded
                console.log(`Content script not responding in Tab ${tabId}, will inject: ${error.message}`);
                return null;
            });

        // If we got a response, the content script is already loaded
        if (pingResult && pingResult.pong) {
            console.log(`Content script already loaded in Tab ${tabId}`);
            return true;
        }

        // Inject the content scripts
        console.log(`Injecting content scripts into Tab ${tabId}...`);

        try {
            // First inject element-utils.js
            await chrome.scripting.executeScript({
                target: { tabId: tabId },
                files: ['sidepanel/common/element-utils.js']
            });

            // Inject content-highlight.js (which now contains the functionality previously in element-validator.js)
            await chrome.scripting.executeScript({
                target: { tabId: tabId },
                files: ['content-highlight.js']
            });

            // Inject the split content files in the correct order
            await chrome.scripting.executeScript({
                target: { tabId: tabId },
                files: ['content-core.js']
            });

            await chrome.scripting.executeScript({
                target: { tabId: tabId },
                files: ['content-hover.js']
            });

            // Inject the content-actions modules in the correct order
            await chrome.scripting.executeScript({
                target: { tabId: tabId },
                files: ['content-actions-basic.js']
            });

            await chrome.scripting.executeScript({
                target: { tabId: tabId },
                files: ['content-actions-select.js']
            });

            await chrome.scripting.executeScript({
                target: { tabId: tabId },
                files: ['content-actions-file.js']
            });

            await chrome.scripting.executeScript({
                target: { tabId: tabId },
                files: ['content-actions-iframe.js']
            });

            await chrome.scripting.executeScript({
                target: { tabId: tabId },
                files: ['content-actions-utils.js']
            });

            await chrome.scripting.executeScript({
                target: { tabId: tabId },
                files: ['content-actions.js']
            });

            await chrome.scripting.executeScript({
                target: { tabId: tabId },
                files: ['content-messaging.js']
            });

            // Verify the scripts were loaded by sending another ping
            const verifyResult = await chrome.tabs.sendMessage(tabId, { action: 'ping' })
                .catch(error => {
                    console.warn(`Verification ping failed after script injection in Tab ${tabId}: ${error.message}`);
                    return null;
                });

            if (verifyResult && verifyResult.pong) {
                console.log(`Content scripts successfully injected and verified in Tab ${tabId}`);
                return true;
            } else {
                console.warn(`Content scripts injected but verification failed in Tab ${tabId}`);
                return false;
            }
        } catch (injectionError) {
            console.error(`Error injecting scripts into Tab ${tabId}:`, injectionError);
            return false;
        }
    } catch (error) {
        console.error(`Error ensuring content scripts in Tab ${tabId}:`, error);
        return false;
    }
}

chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
    const senderType = sender.tab ? `Content Script (Tab ${sender.tab.id})` : sender.url || 'Extension Internal';
    console.log(`Background received: ${message.action} from ${senderType}`);
    let needsAsyncResponse = false;

    try {
        if (message.action === 'proxyRequestToContentScript') {
            needsAsyncResponse = true;
            const { targetTabId, contentScriptAction, payload } = message;

            if (!targetTabId) {
                console.error("Proxy Error: Missing targetTabId in message:", message);
                sendResponse({ success: false, error: "Missing targetTabId for proxy request" });
                return needsAsyncResponse;
            }
            const allowedActions = ['ping', 'enableSelectionMode', 'cancelSelectionMode', 'getFullHTML', 'setElementValue', 'highlightElement', 'clickElement', 'getSelectOptions', 'selectOption', 'setCheckboxState', 'setRadioState', 'validateSelector', 'highlightElementBySelector', 'removeValidationHighlight', 'updateKnownSelectors', 'highlightMultipleElementsBySelectors', 'removeAllHighlights', 'uploadFileFromUrl', 'simulateDragDropFile', 'setIframeValue'];
            if (!allowedActions.includes(contentScriptAction)) {
                 console.error(`Proxy Error: Disallowed content script action requested: ${contentScriptAction}`);
                 sendResponse({ success: false, error: `Action "${contentScriptAction}" cannot be proxied.` });
                 return needsAsyncResponse;
            }

            // Ensure content scripts are loaded before sending the message
            console.log(`Proxy: Preparing to send '${contentScriptAction}' to Tab ${targetTabId}`);

            // Check if the tab is valid before proceeding
            // Use Promise-based approach instead of await since we're in a non-async function

            // Set up a timeout for the operation
            const timeoutId = setTimeout(() => {
                console.error(`Operation timed out after 10 seconds for action '${contentScriptAction}' to Tab ${targetTabId}`);
                sendResponse({
                    success: false,
                    error: `Operation timed out while communicating with Tab ${targetTabId}. The page might be busy or unresponsive.`,
                    action: contentScriptAction,
                    targetTabId: targetTabId
                });
            }, 10000); // 10 seconds

            chrome.tabs.get(targetTabId)
                .then(tab => {
                    if (!tab) {
                        throw new Error(`Tab ${targetTabId} does not exist`);
                    }

                    if (tab.status === 'loading') {
                        console.log(`Tab ${targetTabId} is still loading, waiting briefly before proceeding...`);
                        return new Promise(resolve => setTimeout(() => resolve(tab), 500));
                    }
                    return tab;
                })
                .then(() => {
                    // Continue with the main operation after tab check
                    return ensureContentScriptsLoaded(targetTabId);
                })
                .then(scriptsLoaded => {
                    if (!scriptsLoaded) {
                        throw new Error(`Failed to ensure content scripts are loaded in Tab ${targetTabId}. The page might be reloading or inaccessible.`);
                    }

                    console.log(`Proxying action '${contentScriptAction}' to Tab ${targetTabId}`, payload);
                    return chrome.tabs.sendMessage(targetTabId, { action: contentScriptAction, payload: payload });
                })
                .then(response => {
                    // Clear the timeout since we got a response
                    clearTimeout(timeoutId);
                    console.log(`Received response from content script for '${contentScriptAction}':`, response);
                    sendResponse(response);
                })
                .catch(error => {
                    // Clear the timeout since we got an error response
                    clearTimeout(timeoutId);
                    console.error(`Proxy Error sending '${contentScriptAction}' to Tab ${targetTabId}:`, error);

                    let errorMessage = `Content script communication failed: ${error.message || 'Unknown error'}`;
                    if (error.message?.includes('Could not establish connection') ||
                        error.message?.includes('Receiving end does not exist') ||
                        error.message?.includes('Failed to ensure content scripts')) {
                        errorMessage = `Could not connect to the content script on Tab ${targetTabId}. It might be reloading or inaccessible.`;
                    } else if (error.message?.includes('timed out')) {
                        errorMessage = `Operation timed out while communicating with Tab ${targetTabId}. The page might be busy or unresponsive.`;
                    }

                    // Log the error with more details for debugging
                    console.error(`Proxy Error details for '${contentScriptAction}': ${errorMessage}`);

                    sendResponse({
                        success: false,
                        error: errorMessage,
                        action: contentScriptAction,
                        targetTabId: targetTabId
                    });
                });

                // Return needsAsyncResponse to indicate we'll send a response asynchronously
                return needsAsyncResponse;
        }
        else if (sender.url?.includes('popup.html')) {
            if (message.action === 'reportUpdated') {
                 needsAsyncResponse = false;
                 console.log('Popup updated report, broadcasting to side panel...');
                 notifySidePanel({ action: 'reportUpdated', report: message.report });

            }
        }
        else if (sender.tab) {
            const sourceTabId = sender.tab.id;
            if (message.action === 'elementInfo' || message.action === 'selectionComplete') {
                needsAsyncResponse = false;
                console.log(`Forwarding '${message.action}' from Tab ${sourceTabId} to Side Panel`);

                // Get the sidepanel associated with this tab
                chrome.sidePanel.getOptions({ tabId: sourceTabId })
                    .then(options => {
                        console.log(`Side panel options for Tab ${sourceTabId}:`, options);
                        // Forward the message with the tab ID so sidepanels can filter
                        notifySidePanel({
                            ...message,
                            originatingTabId: sourceTabId,
                            targetTabId: sourceTabId // Add explicit target tab ID
                        });
                    })
                    .catch(error => {
                        console.warn(`Error getting side panel options for Tab ${sourceTabId}:`, error);
                        // Still forward the message with the originating tab ID as fallback
                        notifySidePanel({
                            ...message,
                            originatingTabId: sourceTabId,
                            targetTabId: sourceTabId // Add explicit target tab ID
                        });
                    });
            }
        }
         else if (sender.url?.includes('sidepanel.html')) {
             if (message.action === 'reportUpdated') {
                  needsAsyncResponse = false;
                  console.log('Sidepanel triggered reportUpdated broadcast (if needed)');

             }
         }
         else {
             console.warn(`Background received unhandled message action: ${message.action} from ${senderType}`);
         }

    } catch (error) {
        console.error("Background Error processing message:", message.action, error);
         if (needsAsyncResponse) {
             try {
                 sendResponse({ success: false, error: `Background script error: ${error.message}` });
             } catch (e) {

             }
         }
    }

    return needsAsyncResponse;
});

// Handle fetching images from URLs (bypassing CORS restrictions)
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
    if (message.action === 'fetchImageFromUrl') {
        const imageUrl = message.url;
        console.log('Background: Fetching image from URL:', imageUrl);

        fetch(imageUrl)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`Failed to fetch image: ${response.status} ${response.statusText}`);
                }
                return response.blob();
            })
            .then(blob => {
                // Convert blob to base64
                const reader = new FileReader();
                reader.onloadend = () => {
                    sendResponse({
                        success: true,
                        dataUrl: reader.result,
                        contentType: blob.type,
                        size: blob.size
                    });
                };
                reader.onerror = () => {
                    sendResponse({
                        success: false,
                        error: 'Failed to read image data'
                    });
                };
                reader.readAsDataURL(blob);
            })
            .catch(error => {
                console.error('Error fetching image:', error);
                sendResponse({
                    success: false,
                    error: error.message
                });
            });

        return true; // Indicates we'll send a response asynchronously
    }
});

