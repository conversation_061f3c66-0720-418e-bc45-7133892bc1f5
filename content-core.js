// content-core.js - Core initialization and utility functions

console.log('Content-Core: Loading...');

if (typeof window.enableSelectionMode !== 'function') {
    console.warn('Content-Core: common/element-utils.js not found or loaded yet.');
}

// Note: isElementInViewport is now defined in content-highlight.js
// We'll check if it's already defined before redefining it
if (typeof window.isElementInViewport !== 'function') {
    // Helper function to check if an element is in the viewport
    function isElementInViewport(element) {
        const rect = element.getBoundingClientRect();
        return (
            rect.top >= 0 &&
            rect.left >= 0 &&
            rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
            rect.right <= (window.innerWidth || document.documentElement.clientWidth)
        );
    }

    // Make the function available globally
    window.isElementInViewport = isElementInViewport;
}

// Track all selectors from the sidepanel for hover detection
window.knownSelectors = window.knownSelectors || new Set();

// Function to check if all required content script functions are loaded
function checkContentScriptDependencies() {
    const requiredFunctions = [
        'handleGetSelectOptions',
        'handleSelectOption',
        'handleSetElementValue',
        'handleClickElement',
        'handleSetCheckboxRadioState'
    ];

    const missingFunctions = requiredFunctions.filter(func => typeof window[func] !== 'function');

    if (missingFunctions.length > 0) {
        console.warn('Content-Core: Missing functions:', missingFunctions);
        return false;
    }

    console.log('Content-Core: All required functions are available');
    return true;
}

// Initialize the extension when the document is fully loaded
function initializeExtension() {
    console.log('Content-Core: Initializing extension...');

    try {
        // Check if all dependencies are loaded
        if (!checkContentScriptDependencies()) {
            console.warn('Content-Core: Not all dependencies loaded, retrying in 1 second...');
            setTimeout(initializeExtension, 1000);
            return;
        }

        // Initialize page element hover listeners (defined in content-hover.js)
        // DISABLED: Hover highlighting feature removed per user request
        /*
        if (typeof window.setupPageElementHoverListeners === 'function') {
            window.setupPageElementHoverListeners();
            console.log('Content-Core: Page element hover listeners initialized');
        } else {
            console.warn('Content-Core: setupPageElementHoverListeners function not available yet');
            // Try again after a short delay
            setTimeout(() => {
                if (typeof window.setupPageElementHoverListeners === 'function') {
                    window.setupPageElementHoverListeners();
                    console.log('Content-Core: Page element hover listeners initialized (delayed)');
                } else {
                    console.error('Content-Core: setupPageElementHoverListeners function not available');
                }
            }, 500);
        }
        */

        console.log('Content-Core: Extension initialization complete');
    } catch (e) {
        console.error("Content-Core: Failed to initialize:", e);
    }
}

// Initialize when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeExtension);
} else {
    // DOM is already loaded
    initializeExtension();
}


