// content-actions-select.js - Select element manipulation functions
// Handles: getSelectOptions, selectOption

console.log('Content-Actions-Select: Loading...');

// Handle getting options from a select element or custom-select element
function handleGetSelectOptions(message, sendResponse) {
    const selectSelector = message.payload?.selector;
    const optionSelector = message.payload?.optionSelector;
    const fieldLabel = message.payload?.fieldLabel;
    console.log(`Content-Actions-Select: Attempting to get options from select/custom-select with selector: "${selectSelector}"`);

    if (!selectSelector) {
        console.error('Content-Actions-Select: Missing selector for getSelectOptions.');
        sendResponse({ success: false, error: 'Missing selector for getSelectOptions.' });
        return;
    }

    try {
        const selectElement = document.querySelector(selectSelector);
        if (!selectElement) {
            console.warn(`Content-Actions-Select: Element not found: "${selectSelector}"`);
            sendResponse({ success: false, error: 'Element not found.' });
            return;
        }

        // Handle standard SELECT elements
        if (selectElement.tagName === 'SELECT') {
            console.log(`Content-Actions-Select: Found standard SELECT element with ${selectElement.options.length} options`);

            try {
                // Get all options from the select element
                const options = Array.from(selectElement.options).map((option, index) => {
                    try {
                        return {
                            value: option.value,
                            text: option.textContent.trim()
                        };
                    } catch (optionError) {
                        console.warn(`Content-Actions-Select: Error processing option ${index}:`, optionError);
                        return {
                            value: option.value || `option_${index}`,
                            text: `Option ${index + 1} (error)`
                        };
                    }
                });

                console.log(`Content-Actions-Select: Retrieved ${options.length} options from select ${selectSelector}`);
                sendResponse({
                    success: true,
                    options: options,
                    isCustomSelect: false,
                    fieldLabel: fieldLabel
                });
            } catch (selectError) {
                console.error('Content-Actions-Select: Error processing SELECT options:', selectError);
                sendResponse({ success: false, error: `Error processing SELECT options: ${selectError.message}` });
            }
            return;
        }

        // Handle custom-select elements
        if (!optionSelector) {
            console.warn(`Content-Actions-Select: Element found but not a SELECT and no optionSelector provided: "${selectSelector}"`);
            sendResponse({ success: false, error: 'Element is not a SELECT element and no optionSelector was provided.' });
            return;
        }

        console.log(`Content-Actions-Select: Element is a custom-select. Getting HTML for options using selector: ${optionSelector}`);
        console.log(`Content-Actions-Select: Using single attempt for custom-select options - this is not a native <select>`);

        try {
            // Click the select element to open the dropdown
            selectElement.click();

            // Also dispatch a more complete set of events for better compatibility
            // This ensures the click is properly registered by the page
            ['mousedown', 'mouseup', 'click'].forEach(eventType => {
                try {
                    selectElement.dispatchEvent(new MouseEvent(eventType, {
                        bubbles: true,
                        cancelable: true,
                        view: window
                    }));
                } catch (eventError) {
                    console.warn(`Content-Actions-Select: Error dispatching ${eventType} event:`, eventError);
                }
            });

            // Wait for the dropdown to appear
            setTimeout(() => {
                try {
                    const html = document.querySelector(optionSelector)?.outerHTML || '';
                    console.log(`Content-Actions-Select: Retrieved HTML for custom-select options:`, html);
                    sendResponse({
                        success: true,
                        html: html,
                        isCustomSelect: true,
                        fieldLabel: fieldLabel
                    });
                } catch (timeoutError) {
                    console.error('Content-Actions-Select: Error in custom-select timeout handler:', timeoutError);
                    sendResponse({ success: false, error: `Custom-select timeout error: ${timeoutError.message}` });
                }
            }, 500); // 0.5 second delay as requested

            // Return true to indicate we'll send a response asynchronously
            return true;
        } catch (customSelectError) {
            console.error('Content-Actions-Select: Error handling custom-select:', customSelectError);
            sendResponse({ success: false, error: `Custom-select error: ${customSelectError.message}` });
        }
    } catch (e) {
        console.error(`Content-Actions-Select: Error in getSelectOptions for selector "${selectSelector}":`, e);
        sendResponse({ success: false, error: `Execution error: ${e.message}` });
    }
}

// Handle selecting an option in a select element or custom-select element
function handleSelectOption(message, sendResponse) {
    const { selector: selectOptionSelector, value: optionValue, isCustomSelect } = message.payload || {};

    // For custom-select elements, the selector is the direct path to the option to click
    if (isCustomSelect) {
        console.log(`Attempting to click custom-select option with selector: "${selectOptionSelector}"`);

        try {
            // For custom-select, we directly click the option element using the AI-generated selector
            // No need for multiple attempts - just click once
            const optionElement = document.querySelector(selectOptionSelector);
            if (optionElement) {
                console.log(`Found custom-select option element. Clicking it.`);
                optionElement.click();

                // Dispatch events to ensure the click is registered
                ['mousedown', 'mouseup', 'click'].forEach(eventType => {
                    optionElement.dispatchEvent(new MouseEvent(eventType, {
                        bubbles: true,
                        cancelable: true,
                        view: window
                    }));
                });

                console.log(`Option clicked successfully with selector ${selectOptionSelector}`);
                sendResponse({ success: true });
            } else {
                console.warn(`Custom-select option element not found with selector: "${selectOptionSelector}"`);
                sendResponse({ success: false, error: 'Custom-select option element not found.' });
            }
            return;
        } catch (e) {
            console.error(`Error in selectOption for custom-select with selector "${selectOptionSelector}":`, e);
            sendResponse({ success: false, error: `Execution error: ${e.message}` });
            return;
        }
    }

    // Handle standard SELECT elements
    console.log(`Attempting to select option with value "${optionValue}" in select with selector: "${selectOptionSelector}"`);

    if (selectOptionSelector && optionValue !== undefined) {
        try {
            const selectElement = document.querySelector(selectOptionSelector);
            if (!selectElement) {
                console.warn(`Element not found: "${selectOptionSelector}"`);
                sendResponse({ success: false, error: 'Element not found.' });
                return;
            }

            if (selectElement.tagName === 'SELECT') {
                // Find the option with the matching value
                const option = Array.from(selectElement.options).find(opt =>
                    opt.value === optionValue || opt.textContent.trim() === optionValue
                );

                if (option) {
                    // Set the select value
                    selectElement.value = option.value;

                    // Dispatch change event
                    selectElement.dispatchEvent(new Event('change', { bubbles: true }));

                    console.log(`Option selected successfully in ${selectOptionSelector}`);
                    sendResponse({ success: true });
                } else {
                    console.warn(`Option with value/text "${optionValue}" not found in select`);
                    sendResponse({ success: false, error: 'Option not found in select element.' });
                }
            } else {
                console.warn(`Element is not a SELECT: "${selectOptionSelector}"`);
                sendResponse({ success: false, error: 'Element is not a SELECT element.' });
            }
        } catch (e) {
            console.error(`Error in selectOption for selector "${selectOptionSelector}":`, e);
            sendResponse({ success: false, error: `Execution error: ${e.message}` });
        }
    } else {
        sendResponse({ success: false, error: 'Missing selector or value for selectOption.' });
    }
}

// Make the functions available globally
window.handleGetSelectOptions = handleGetSelectOptions;
window.handleSelectOption = handleSelectOption;

console.log('Content-Actions-Select: Select element manipulation functions loaded');
