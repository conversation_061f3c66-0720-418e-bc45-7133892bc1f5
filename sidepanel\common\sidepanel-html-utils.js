// sidepanel-html-utils.js
// Handles HTML processing and utility functions

// Minimize HTML for AI processing
function minimizeHtmlRegex(htmlString, focusOnBody = false) {
    let minimizedHtml = htmlString;
    try {
        if (focusOnBody) {
           const bodyMatch = minimizedHtml.match(/<body[^>]*>([\s\S]*?)<\/body>/i);
           minimizedHtml = (bodyMatch && bodyMatch[1]) ? bodyMatch[1].trim() : minimizedHtml;
        }
        minimizedHtml = minimizedHtml.replace(/<!--[\s\S]*?-->/g, '');
        minimizedHtml = minimizedHtml.replace(/<script[^>]*>[\s\S]*?<\/script>/gi, '');
        minimizedHtml = minimizedHtml.replace(/<style[^>]*>[\s\S]*?<\/style>/gi, '');
        minimizedHtml = minimizedHtml.replace(/<select([^>]*)>([\s\S]*?)<\/select>/gi, (_, selectAttrs) => {
            return `<select${selectAttrs}></select>`;
        });
        const tagsToRemove = ['img', 'svg', 'picture', 'video', 'audio', 'iframe', 'noscript', 'canvas', 'link', 'meta'];
        tagsToRemove.forEach(tag => {
            const regex = new RegExp(`<${tag}[^>]*>[\\s\\S]*?<\\/${tag}>|<${tag}[^>]*\\/?>`, 'gi');
            minimizedHtml = minimizedHtml.replace(regex, '');
        });
         minimizedHtml = minimizedHtml.replace(/\sstyle="[^"]*"/gi, '');
         minimizedHtml = minimizedHtml.replace(/\s\s+/g, ' ');
         minimizedHtml = minimizedHtml.replace(/>\s+</g, '><').trim();
        return minimizedHtml;
    } catch (error) {
        console.error("HTML-Utils: Error minimizing HTML:", error);
        return htmlString;
    }
}

// Unescape HTML strings
function unescapeString(str) {
     if (typeof str !== 'string') return str;
    try {
        const parser = new DOMParser();
        const doc = parser.parseFromString(str, 'text/html');
        let decoded = doc.documentElement.textContent;
        return decoded;
    } catch (e) {
        return str;
    }
}



