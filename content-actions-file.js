// content-actions-file.js - File upload and drag-drop manipulation functions
// Handles: uploadFileFromUrl, simulateDragDropFile

// Handle uploading a file from a URL to a file input element
function handleUploadFileFromUrl(message, sendResponse) {
    const { selector, imageUrl, filename } = message.payload || {};
    console.log(`Attempting to upload file from URL "${imageUrl}" to file input "${selector}"`);

    if (!selector || !imageUrl) {
        sendResponse({ success: false, error: 'Missing selector or imageUrl for uploadFileFromUrl.' });
        return;
    }

    try {
        // Find the file input element
        const fileInput = document.querySelector(selector);
        if (!fileInput || fileInput.tagName !== 'INPUT' || fileInput.type !== 'file') {
            console.warn(`Element not found or not a file input: "${selector}"`);
            sendResponse({ success: false, error: 'Element not found or not a file input.' });
            return;
        }

        // Use the background script to fetch the image (bypassing CORS)
        console.log(`Requesting background script to fetch image from URL: ${imageUrl}`);
        chrome.runtime.sendMessage({
            action: 'fetchImageFromUrl',
            url: imageUrl
        }, response => {
            if (!response || !response.success) {
                const errorMsg = response?.error || 'Unknown error fetching image';
                console.error(`Background fetch error: ${errorMsg}`);
                sendResponse({ success: false, error: errorMsg });
                return;
            }

            try {
                // Convert data URL to blob
                const dataUrl = response.dataUrl;
                const contentType = response.contentType || 'image/jpeg';

                // Extract the base64 data from the data URL
                const base64Data = dataUrl.split(',')[1];
                const byteCharacters = atob(base64Data);
                const byteArrays = [];

                for (let i = 0; i < byteCharacters.length; i += 512) {
                    const slice = byteCharacters.slice(i, i + 512);
                    const byteNumbers = new Array(slice.length);

                    for (let j = 0; j < slice.length; j++) {
                        byteNumbers[j] = slice.charCodeAt(j);
                    }

                    byteArrays.push(new Uint8Array(byteNumbers));
                }

                const blob = new Blob(byteArrays, { type: contentType });

                // Determine filename and extension
                let finalFilename = filename || 'image';
                const extension = contentType.split('/')[1] || 'jpg';
                if (!finalFilename.includes('.')) {
                    finalFilename = `${finalFilename}.${extension}`;
                }

                // Create a File object from the blob
                const file = new File([blob], finalFilename, { type: contentType });

                // Create a DataTransfer object to set the file on the input
                const dataTransfer = new DataTransfer();
                dataTransfer.items.add(file);

                // Set the files property on the file input
                fileInput.files = dataTransfer.files;

                // Dispatch events to ensure the change is registered
                fileInput.dispatchEvent(new Event('change', { bubbles: true }));
                fileInput.dispatchEvent(new Event('input', { bubbles: true }));

                console.log(`Successfully uploaded file from URL to file input: "${selector}"`);
                sendResponse({ success: true });
            } catch (error) {
                console.error(`Error processing image data:`, error);
                sendResponse({ success: false, error: `Error processing image: ${error.message}` });
            }
        });

        // Return true to indicate we'll send a response asynchronously
        return true;
    } catch (error) {
        console.error(`Error in uploadFileFromUrl handler:`, error);
        sendResponse({ success: false, error: `Error in upload handler: ${error.message}` });
    }
}

// Handle simulating drag and drop of a file from a URL
function handleSimulateDragDropFile(message, sendResponse) {
    const { selector, imageUrl, filename } = message.payload || {};
    console.log(`Attempting to simulate drag and drop of file from URL "${imageUrl}" to element "${selector}"`);

    if (!selector || !imageUrl) {
        sendResponse({ success: false, error: 'Missing selector or imageUrl for simulateDragDropFile.' });
        return;
    }

    try {
        // Find the target element
        const dropTarget = document.querySelector(selector);
        if (!dropTarget) {
            console.warn(`Drop target element not found: "${selector}"`);
            sendResponse({ success: false, error: 'Drop target element not found.' });
            return;
        }

        // Use the background script to fetch the image (bypassing CORS)
        console.log(`Requesting background script to fetch image from URL: ${imageUrl}`);
        chrome.runtime.sendMessage({
            action: 'fetchImageFromUrl',
            url: imageUrl
        }, response => {
            if (!response || !response.success) {
                const errorMsg = response?.error || 'Unknown error fetching image';
                console.error(`Background fetch error: ${errorMsg}`);
                sendResponse({ success: false, error: errorMsg });
                return;
            }

            try {
                // Convert data URL to blob
                const dataUrl = response.dataUrl;
                const contentType = response.contentType || 'image/jpeg';

                // Extract the base64 data from the data URL
                const base64Data = dataUrl.split(',')[1];
                const byteCharacters = atob(base64Data);
                const byteArrays = [];

                for (let i = 0; i < byteCharacters.length; i += 512) {
                    const slice = byteCharacters.slice(i, i + 512);
                    const byteNumbers = new Array(slice.length);

                    for (let j = 0; j < slice.length; j++) {
                        byteNumbers[j] = slice.charCodeAt(j);
                    }

                    byteArrays.push(new Uint8Array(byteNumbers));
                }

                const blob = new Blob(byteArrays, { type: contentType });

                // Determine filename and extension
                let finalFilename = filename || 'image';
                const extension = contentType.split('/')[1] || 'jpg';
                if (!finalFilename.includes('.')) {
                    finalFilename = `${finalFilename}.${extension}`;
                }

                // Create a File object from the blob
                const file = new File([blob], finalFilename, { type: contentType });

                // Create a DataTransfer object for the drag event
                const dataTransfer = new DataTransfer();
                dataTransfer.items.add(file);

                // Create and dispatch drag events with proper try/catch for browser compatibility
                try {
                    // First try with DragEvent constructor
                    const dragEnter = new DragEvent('dragenter', { bubbles: true, cancelable: true, dataTransfer });
                    const dragOver = new DragEvent('dragover', { bubbles: true, cancelable: true, dataTransfer });
                    const drop = new DragEvent('drop', { bubbles: true, cancelable: true, dataTransfer });

                    dropTarget.dispatchEvent(dragEnter);
                    dropTarget.dispatchEvent(dragOver);
                    dropTarget.dispatchEvent(drop);
                } catch (dragEventError) {
                    console.log('DragEvent constructor not supported, falling back to createEvent method');

                    // Fallback for browsers that don't support DragEvent constructor
                    try {
                        // Create events using createEvent
                        const dragEnter = document.createEvent('DragEvent');
                        const dragOver = document.createEvent('DragEvent');
                        const drop = document.createEvent('DragEvent');

                        // Initialize the events
                        dragEnter.initDragEvent('dragenter', true, true, window, 0, 0, 0, 0, 0, false, false, false, false, 0, null, dataTransfer);
                        dragOver.initDragEvent('dragover', true, true, window, 0, 0, 0, 0, 0, false, false, false, false, 0, null, dataTransfer);
                        drop.initDragEvent('drop', true, true, window, 0, 0, 0, 0, 0, false, false, false, false, 0, null, dataTransfer);

                        // Dispatch the events
                        dropTarget.dispatchEvent(dragEnter);
                        dropTarget.dispatchEvent(dragOver);
                        dropTarget.dispatchEvent(drop);
                    } catch (fallbackError) {
                        console.log('Fallback method also failed, using CustomEvent as last resort');

                        // Last resort: use CustomEvent with dataTransfer in detail
                        const dragEnter = new CustomEvent('dragenter', { bubbles: true, cancelable: true, detail: { dataTransfer } });
                        const dragOver = new CustomEvent('dragover', { bubbles: true, cancelable: true, detail: { dataTransfer } });
                        const drop = new CustomEvent('drop', { bubbles: true, cancelable: true, detail: { dataTransfer } });

                        dropTarget.dispatchEvent(dragEnter);
                        dropTarget.dispatchEvent(dragOver);
                        dropTarget.dispatchEvent(drop);
                    }
                }

                // Also dispatch change and input events
                dropTarget.dispatchEvent(new Event('change', { bubbles: true }));
                dropTarget.dispatchEvent(new Event('input', { bubbles: true }));

                console.log(`Successfully simulated drag and drop of file to element: "${selector}"`);
                sendResponse({ success: true });
            } catch (error) {
                console.error(`Error processing image data:`, error);
                sendResponse({ success: false, error: `Error processing image: ${error.message}` });
            }
        });

        // Return true to indicate we'll send a response asynchronously
        return true;
    } catch (error) {
        console.error(`Error in simulateDragDropFile handler:`, error);
        sendResponse({ success: false, error: `Error in drag drop handler: ${error.message}` });
    }
}

// Make the functions available globally
window.handleUploadFileFromUrl = handleUploadFileFromUrl;
window.handleSimulateDragDropFile = handleSimulateDragDropFile;

console.log('Content-Actions-File: File manipulation functions loaded');
