// content-actions.js - Main dispatcher for element manipulation functions
// This file coordinates all element action handlers from separate modules

console.log('Content-Actions: Loading...');

// Function to wait for a handler to become available
function waitForHandler(handlerName, maxWaitTime = 5000) {
    return new Promise((resolve) => {
        const startTime = Date.now();

        function checkHandler() {
            if (typeof window[handlerName] === 'function') {
                resolve(true);
            } else if (Date.now() - startTime < maxWaitTime) {
                setTimeout(checkHandler, 100);
            } else {
                resolve(false);
            }
        }

        checkHandler();
    });
}

// Handler for all element action messages
async function handleElementAction(message, sendResponse) {
    const action = message.action;

    console.log(`Content-Actions: Received action: ${action}`, message);

    try {
        switch (action) {
            // Basic element operations (handled in content-actions-basic.js)
            case 'setElementValue':
                if (typeof window.handleSetElementValue === 'function') {
                    window.handleSetElementValue(message, sendResponse);
                } else {
                    console.warn('Content-Actions: handleSetElementValue not available, waiting...');
                    const available = await waitForHandler('handleSetElementValue');
                    if (available) {
                        window.handleSetElementValue(message, sendResponse);
                    } else {
                        console.error('Content-Actions: handleSetElementValue not available after waiting');
                        sendResponse({ success: false, error: 'Handler not available after waiting' });
                    }
                }
                break;
            case 'clickElement':
                if (typeof window.handleClickElement === 'function') {
                    window.handleClickElement(message, sendResponse);
                } else {
                    console.warn('Content-Actions: handleClickElement not available, waiting...');
                    const available = await waitForHandler('handleClickElement');
                    if (available) {
                        window.handleClickElement(message, sendResponse);
                    } else {
                        console.error('Content-Actions: handleClickElement not available after waiting');
                        sendResponse({ success: false, error: 'Handler not available after waiting' });
                    }
                }
                break;
            case 'setCheckboxState':
            case 'setRadioState':
                if (typeof window.handleSetCheckboxRadioState === 'function') {
                    window.handleSetCheckboxRadioState(message, sendResponse);
                } else {
                    console.warn('Content-Actions: handleSetCheckboxRadioState not available, waiting...');
                    const available = await waitForHandler('handleSetCheckboxRadioState');
                    if (available) {
                        window.handleSetCheckboxRadioState(message, sendResponse);
                    } else {
                        console.error('Content-Actions: handleSetCheckboxRadioState not available after waiting');
                        sendResponse({ success: false, error: 'Handler not available after waiting' });
                    }
                }
                break;

            // Select element operations (handled in content-actions-select.js)
            case 'getSelectOptions':
                if (typeof window.handleGetSelectOptions === 'function') {
                    window.handleGetSelectOptions(message, sendResponse);
                } else {
                    console.warn('Content-Actions: handleGetSelectOptions not available, waiting...');
                    const available = await waitForHandler('handleGetSelectOptions');
                    if (available) {
                        window.handleGetSelectOptions(message, sendResponse);
                    } else {
                        console.error('Content-Actions: handleGetSelectOptions not available after waiting');
                        sendResponse({ success: false, error: 'Handler not available after waiting' });
                    }
                }
                break;
            case 'selectOption':
                if (typeof window.handleSelectOption === 'function') {
                    window.handleSelectOption(message, sendResponse);
                } else {
                    console.warn('Content-Actions: handleSelectOption not available, waiting...');
                    const available = await waitForHandler('handleSelectOption');
                    if (available) {
                        window.handleSelectOption(message, sendResponse);
                    } else {
                        console.error('Content-Actions: handleSelectOption not available after waiting');
                        sendResponse({ success: false, error: 'Handler not available after waiting' });
                    }
                }
                break;

            // File operations (handled in content-actions-file.js)
            case 'uploadFileFromUrl':
                if (typeof window.handleUploadFileFromUrl === 'function') {
                    window.handleUploadFileFromUrl(message, sendResponse);
                } else {
                    console.warn('Content-Actions: handleUploadFileFromUrl not available, waiting...');
                    const available = await waitForHandler('handleUploadFileFromUrl');
                    if (available) {
                        window.handleUploadFileFromUrl(message, sendResponse);
                    } else {
                        console.error('Content-Actions: handleUploadFileFromUrl not available after waiting');
                        sendResponse({ success: false, error: 'Handler not available after waiting' });
                    }
                }
                break;
            case 'simulateDragDropFile':
                if (typeof window.handleSimulateDragDropFile === 'function') {
                    window.handleSimulateDragDropFile(message, sendResponse);
                } else {
                    console.warn('Content-Actions: handleSimulateDragDropFile not available, waiting...');
                    const available = await waitForHandler('handleSimulateDragDropFile');
                    if (available) {
                        window.handleSimulateDragDropFile(message, sendResponse);
                    } else {
                        console.error('Content-Actions: handleSimulateDragDropFile not available after waiting');
                        sendResponse({ success: false, error: 'Handler not available after waiting' });
                    }
                }
                break;

            // HTML retrieval (handled in content-actions-utils.js)
            case 'getFullHTML':
                if (typeof window.handleGetFullHTML === 'function') {
                    window.handleGetFullHTML(message, sendResponse);
                } else {
                    console.warn('Content-Actions: handleGetFullHTML not available, waiting...');
                    const available = await waitForHandler('handleGetFullHTML');
                    if (available) {
                        window.handleGetFullHTML(message, sendResponse);
                    } else {
                        console.error('Content-Actions: handleGetFullHTML not available after waiting');
                        sendResponse({ success: false, error: 'Handler not available after waiting' });
                    }
                }
                break;

            // Iframe operations (handled in content-actions-iframe.js)
            case 'setIframeValue':
                if (typeof window.handleSetIframeValue === 'function') {
                    window.handleSetIframeValue(message, sendResponse);
                } else {
                    console.warn('Content-Actions: handleSetIframeValue not available, waiting...');
                    const available = await waitForHandler('handleSetIframeValue');
                    if (available) {
                        window.handleSetIframeValue(message, sendResponse);
                    } else {
                        console.error('Content-Actions: handleSetIframeValue not available after waiting');
                        sendResponse({ success: false, error: 'Handler not available after waiting' });
                    }
                }
                break;

            default:
                console.warn(`Content-Actions: Unknown element action: ${action}`);
                sendResponse({ success: false, error: `Unknown action: ${action}` });
        }
    } catch (error) {
        console.error('Content-Actions: Error in handleElementAction:', error);
        sendResponse({ success: false, error: `Action handler error: ${error.message}` });
    }
}

// Make the main handler available globally
window.handleElementAction = handleElementAction;

console.log('Content-Actions: Main dispatcher loaded');
