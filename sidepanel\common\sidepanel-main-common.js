// sidepanel-main-common.js

console.log("sidepanel-main-common.js loading");

const tabsContainerMain = document.getElementById('tabs');

function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func.apply(this, args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

document.addEventListener('DOMContentLoaded', initializeSidePanel);

async function initializeSidePanel() {
    console.log('Main-Common: Initializing side panel...');
    if (!tabsContainerMain) {
        console.error("Main-Common: Tabs container not found!");
        return;
    }
     try {
        $(tabsContainerMain).tabs();
        console.log("Main-Common: jQuery UI tabs initialized.");
     } catch (e) {
         console.error("Main-Common: Failed to initialize jQuery UI tabs:", e);
     }

    // Event listeners for UI elements within the side panel itself
    setupCommonEventListeners(); // General listeners
    setupAdminEventListeners();  // Admin tab specific listeners
    setupSubmitEventListeners(); // Submit tab specific listeners
    setupToolsEventListeners();  // Tools tab specific listeners

    // Event listeners for browser events (tabs, storage, messages)
    setupLogicListeners();

    // Initial content load for the currently active tab
    await updateContentForActiveTab();
    console.log('Main-Common: Side panel initialization complete.');
}

function setupCommonEventListeners() {
    console.log("Main-Common: Setting up common event listeners...");

    // Add global keyboard event listener for ESC key to cancel selection mode
    document.addEventListener('keydown', async (e) => {
        if (e.key === 'Escape') {
            console.log("Main-Common: ESC key pressed in sidepanel");
            console.log("Main-Common: Current state - tabId:", state.currentTabId, "isGeneratingSelectorFor:", state.isGeneratingSelectorFor);

            // Check if we're in selection mode using the global flag
            const isSelectionModeActive = window.isSelectionModeActiveInSidepanel === true;

            console.log("Main-Common: isSelectionModeActiveInSidepanel:", isSelectionModeActive);

            // Only send cancel if we have an active tab and we're in selection mode
            if (state.currentTabId && isSelectionModeActive) {
                console.log("Main-Common: Attempting to cancel selection mode via ESC key in sidepanel");

                try {
                    // Send message to content script to cancel selection mode
                    const response = await chrome.runtime.sendMessage({
                        action: 'proxyRequestToContentScript',
                        targetTabId: state.currentTabId,
                        contentScriptAction: 'cancelSelectionMode',
                        payload: {}
                    });

                    // Reset UI state regardless of response
                    resetSelectButtons();
                    state.isGeneratingSelectorFor = null;
                    window.isSelectorGenerationMode = false;

                    console.log("Main-Common: Selection mode cancel request sent from sidepanel", response);
                } catch (error) {
                    console.error("Main-Common: Error sending cancel selection request:", error);
                    // Still reset UI state on error
                    resetSelectButtons();
                    state.isGeneratingSelectorFor = null;
                    window.isSelectorGenerationMode = false;
                }
            } else {
                console.log("Main-Common: ESC key pressed but no active selection mode detected");
            }
        }
    });
}

let logicListenersAttached = false;

function setupLogicListeners() {
     if (logicListenersAttached) {
         console.warn("Main-Common: Attempted to attach logic listeners more than once. Skipping.");
         return;
     }
     // Listen for changes in the active tab
     chrome.tabs.onActivated.addListener(handleTabActivation);
     // Listen for updates to tabs (like URL changes or loading complete)
     chrome.tabs.onUpdated.addListener(handleTabUpdate);
     // Listen for changes in chrome.storage.local (e.g., activeReport change)
     chrome.storage.onChanged.addListener(handleStorageChange); // logic-common
     // Listen for messages from background or content scripts
     chrome.runtime.onMessage.addListener(handleRuntimeMessage); // logic-common
     logicListenersAttached = true;
     console.log("Main-Common: Logic listeners attached.");
}

const debouncedUpdateContentForActiveTab = debounce(updateContentForActiveTab, 350);

async function updateContentForActiveTab() {
    console.log("Main-Common: Updating content for active tab...");
    const tab = await getCurrentActiveTab(); // Get details of the current tab

    // --- ADD GUARD HERE ---
    if (tab && tab.url && !tab.url.startsWith('chrome://') && !tab.url.startsWith('about:')) {
        console.log(`Main-Common: Valid tab found: ${tab.id}, URL: ${tab.url}`);
        const urlChanged = state.currentUrl !== tab.url;
        state.currentUrl = tab.url; // Update state

        // Only reload everything if URL changed, otherwise maybe just refresh report data?
        // For simplicity now, reload both site and report data.
        await loadActiveReportDataAndUpdateUI(); // logic-common -> logic-submit -> ui-submit
        await loadSiteJSONDataAndUpdateUI(tab.url); // logic-admin -> ui-admin/ui-submit
    } else {
        const reason = !tab ? "No active tab" : (!tab.url ? "Tab has no URL" : `Invalid URL (${tab.url})`);
        console.log(`Main-Common: ${reason}, clearing UI.`);
        state.currentUrl = null; // Clear URL state
        clearSidePanelContent(); // ui-common -> ui-admin/ui-submit
    }
}

async function getCurrentActiveTab() {
    console.log("Main-Common: Getting current active tab...");
    try {
        const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
        if (tab) {
            // Update state.currentTabId if it changed
            if (state.currentTabId !== tab.id) {
                console.log(`Main-Common: Active tab ID changed to ${tab.id}`);
                state.currentTabId = tab.id;
            }
            return tab;
        } else {
            console.warn("Main-Common: No active tab found via query");
            if (state.currentTabId !== null) {
                 console.log(`Main-Common: Clearing active tab ID.`);
                 state.currentTabId = null;
            }
            return null;
        }
    } catch (error) {
        console.error("Main-Common: Error getting active tab:", error);
        if (state.currentTabId !== null) {
             console.log(`Main-Common: Clearing active tab ID due to error.`);
             state.currentTabId = null;
        }
        return null;
    }
}

function handleTabActivation(activeInfo) {
    console.log(`Main-Common: Tab activated: ${activeInfo.tabId}`);
    // Update the state immediately
    state.currentTabId = activeInfo.tabId;
    // Debounce the content update to avoid rapid firing if activation events are multiple
    debouncedUpdateContentForActiveTab();
}

// Listen only for relevant tab updates
function handleTabUpdate(tabId, changeInfo, tab) {
    // Only care about updates to the currently active tab
    if (tabId === state.currentTabId) {
        // Relevant updates: URL change OR status becomes 'complete' for a valid URL
        const isRelevantUpdate = changeInfo.url ||
                                (changeInfo.status === 'complete' && tab.url && !tab.url.startsWith('chrome://') && !tab.url.startsWith('about:'));

        if (isRelevantUpdate) {
            console.log(`Main-Common: Active tab ${tabId} updated (${changeInfo.url ? 'URL changed' : 'Load complete'}). Triggering content refresh.`);
            debouncedUpdateContentForActiveTab();
        } else if (changeInfo.status) {
            // Log other status changes for debugging if needed
            // console.log(`Main-Common: Active tab ${tabId} status changed: ${changeInfo.status}`);
        }
    }
}

console.log("sidepanel-main-common.js loaded");