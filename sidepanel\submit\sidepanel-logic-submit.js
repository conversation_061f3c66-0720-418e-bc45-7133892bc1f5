// sidepanel-logic-submit.js
// Handles logic specific to the Submit tab functionality.

console.log("sidepanel-logic-submit.js loading");

// --- Submit Data Loading ---

async function loadReportFieldValuesAndUpdateUI(reportId) {
    if (!reportId) {
        state.reportFieldValues = {}; // Update common state
        updateSubmitFormFields(state.siteFormData, state.siteActionData || []); // Update UI (ui-submit)
        return;
    }
    console.log(`Logic-Submit: Loading report field values for report ID: ${reportId}`);
    try {
        const result = await getFormFields(reportId); // from api-service.js
        if (result.status === 'success' && result.fields) {
            state.reportFieldValues = result.fields; // Update common state
            console.log('Logic-Submit: Report field values loaded.');
        } else {
            console.error('Logic-Submit: Failed to load report field values:', result.message);
            state.reportFieldValues = {}; // Update common state
        }
    } catch (error) {
        console.error('Logic-Submit: Error fetching report field values:', error);
        state.reportFieldValues = {}; // Update common state
    } finally {
        updateSubmitFormFields(state.siteFormData, state.siteActionData || []); // Always update UI (ui-submit)
    }
}

async function fetchReportSiteDataAndUpdateUI() {
    if (!state.activeReport?.report_id || !state.currentSiteId) {
        console.log("Logic-Submit: Skipping fetchReportSiteData - Missing report/site ID.", { reportId: state.activeReport?.report_id, siteId: state.currentSiteId });
        clearReportSiteFields(); // Update UI (ui-submit)
        return;
    }
    const reportId = state.activeReport.report_id;
    const siteId = state.currentSiteId;
    console.log(`Logic-Submit: Fetching report site data for Report ${reportId}, Site ${siteId}`);
    try {
        const response = await getReportSite(reportId, siteId); // from api-service.js
        if (response.status === 'success' && response.report_site) {
            populateReportSiteFields(response.report_site); // Update UI (ui-submit)
        } else if (response.status === 'not-found') {
             console.log('Logic-Submit: No report site data found.');
             clearReportSiteFields(); // Update UI (ui-submit)
        } else {
            console.error('Logic-Submit: Error fetching report site data:', response.message);
            clearReportSiteFields(); // Update UI (ui-submit)
        }
    } catch (error) {
        console.error('Logic-Submit: Error fetching report site data:', error);
        clearReportSiteFields(); // Update UI (ui-submit)
    }
}

// --- Submit Core Processing Logic ---
// (Match & Fill logic might stay primarily in main-submit for simplicity,
// unless it becomes very complex and needs stateful processing here)

console.log("sidepanel-logic-submit.js loaded");