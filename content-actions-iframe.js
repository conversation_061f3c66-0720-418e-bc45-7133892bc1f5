// content-actions-iframe.js - Iframe manipulation functions
// Handles: setIframeValue for custom-textarea fields

// Handle setting a value in an iframe editor (for custom-textarea fields)
function handleSetIframeValue(message, sendResponse) {
    const { selector: iframeSelector, value: textToInsert } = message.payload || {};
    console.log(`Content-Actions: Attempting to set iframe value for "${iframeSelector}" to "${textToInsert}"`);

    if (!iframeSelector || textToInsert === undefined) {
        sendResponse({ success: false, error: 'Missing selector or value for setIframeValue.' });
        return;
    }

    try {
        const iframe = document.querySelector(iframeSelector);
        if (!iframe) {
            console.log(`Content-Actions: Iframe not found: ${iframeSelector}`);
            sendResponse({ success: false, error: 'Iframe not found' });
            return;
        }

        if (!iframe.contentWindow || !iframe.contentWindow.document) {
            console.log(`Content-Actions: Iframe content not accessible due to CSP restrictions`);
            sendResponse({ success: false, error: 'Iframe content not accessible' });
            return;
        }

        const iframeDoc = iframe.contentWindow.document;
        const editableElement = iframeDoc.body;

        if (!editableElement) {
            sendResponse({ success: false, error: 'No editable element found in iframe' });
            return;
        }

        // Focus the element
        editableElement.focus();

        // Set the content
        editableElement.innerHTML = `<p>${textToInsert}</p>`;

        // Dispatch events to notify the editor of changes
        const inputEvent = new iframe.contentWindow.Event('input', { bubbles: true, cancelable: true });
        editableElement.dispatchEvent(inputEvent);

        const changeEvent = new iframe.contentWindow.Event('change', { bubbles: true, cancelable: true });
        editableElement.dispatchEvent(changeEvent);

        console.log(`Content-Actions: Successfully set iframe value`);
        sendResponse({ success: true });

    } catch (error) {
        console.log(`Content-Actions: Iframe access failed: ${error.message}`);
        sendResponse({ success: false, error: error.message });
    }
}



// Make the functions available globally
window.handleSetIframeValue = handleSetIframeValue;

console.log('Content-Actions-Iframe: Iframe manipulation functions loaded (supports custom-textarea)');
