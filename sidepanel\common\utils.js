/**
 * General Utility Functions
 */

/**
 * Format JSON data with proper indentation.
 * @param {any} data - The data to format as JSON.
 * @returns {string} Formatted JSON string or error message.
 */
function formatJSON(data) {
  try {
    return JSON.stringify(data, null, 2); // Use 2 spaces for indentation
  } catch (error) {
    console.error("Error formatting JSON:", error);
    return `{"error": "Could not format JSON: ${error.message}"}`;
  }
}

/**
 * Escape HTML special characters to prevent XSS or display issues.
 * Relies on the browser's built-in capabilities for robustness.
 * @param {string} str - The string to escape.
 * @returns {string} Escaped string.
 */
function escapeHtml(str) {
  if (typeof str !== 'string') return str; // Return non-strings as is

  const element = document.createElement('div');
  element.textContent = str;
  return element.innerHTML;

  // Manual replacement (less robust but fallback)
  // return str
  //   .replace(/&/g, '&amp;')
  //   .replace(/</g, '&lt;')
  //   .replace(/>/g, '&gt;')
  //   .replace(/"/g, '&quot;')
  //   .replace(/'/g, '&#039;');
}


// Add other general utilities here if needed later.
