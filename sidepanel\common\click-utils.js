// click-utils.js - Utility functions for clicking elements

/**
 * Utility functions for clicking elements, with special handling for custom-select elements
 * This ensures consistent behavior across different contexts (form submission, tools, etc.)
 */

// Click an element using the most reliable method for the element type
async function clickElement(selector, tabId) {
    console.log(`Click-Utils: Attempting to click element with selector: "${selector}"`);

    if (!selector || !tabId) {
        console.error('Click-Utils: Missing selector or tabId');
        return { success: false, error: 'Missing selector or tabId' };
    }

    try {
        // Send message to content script to click the element
        const response = await chrome.runtime.sendMessage({
            action: 'proxyRequestToContentScript',
            targetTabId: tabId,
            contentScriptAction: 'clickElement',
            payload: { selector: selector.trim() }
        });

        if (response && response.success) {
            console.log(`Click-Utils: Successfully clicked element with selector: "${selector}"`);
            return { success: true };
        } else {
            const errorMsg = response?.error || 'Unknown error';
            console.error(`Click-Utils: Failed to click element: ${errorMsg}`);
            return { success: false, error: errorMsg };
        }
    } catch (error) {
        console.error('Click-Utils: Error sending click request:', error);
        return { success: false, error: error.message };
    }
}



// Export the functions
window.clickUtils = {
    clickElement
};

console.log('click-utils.js loaded');
